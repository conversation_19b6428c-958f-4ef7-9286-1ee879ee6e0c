{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Phprojects\\ph12-Assignment02\\flutter_app\\build\\.cxx\\Debug\\6l3n3zm3\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Phprojects\\ph12-Assignment02\\flutter_app\\build\\.cxx\\Debug\\6l3n3zm3\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
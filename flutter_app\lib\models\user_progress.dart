import 'weakness_analysis.dart';

class UserProgress {
  final String userId;
  final Map<String, TopicProgress> topicProgress;
  final WeaknessAnalysis weaknessAnalysis;
  final int totalQuizzesTaken;
  final int totalQuestionsAnswered;
  final int totalCorrectAnswers;
  final double overallAccuracy;
  final int studyStreakDays;
  final DateTime lastStudyDate;
  final int totalStudyTimeMinutes;
  final List<String> achievements;
  final Map<String, double> skillScores; // writing, reading, etc.

  const UserProgress({
    required this.userId,
    this.topicProgress = const {},
    required this.weaknessAnalysis,
    this.totalQuizzesTaken = 0,
    this.totalQuestionsAnswered = 0,
    this.totalCorrectAnswers = 0,
    this.overallAccuracy = 0.0,
    this.studyStreakDays = 0,
    required this.lastStudyDate,
    this.totalStudyTimeMinutes = 0,
    this.achievements = const [],
    this.skillScores = const {},
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'topicProgress': topicProgress.map(
        (key, value) => MapEntry(key, value.toMap()),
      ),
      'weaknessAnalysis': weaknessAnalysis.toMap(),
      'totalQuizzesTaken': totalQuizzesTaken,
      'totalQuestionsAnswered': totalQuestionsAnswered,
      'totalCorrectAnswers': totalCorrectAnswers,
      'overallAccuracy': overallAccuracy,
      'studyStreakDays': studyStreakDays,
      'lastStudyDate': lastStudyDate.toIso8601String(),
      'totalStudyTimeMinutes': totalStudyTimeMinutes,
      'achievements': achievements,
      'skillScores': skillScores,
    };
  }

  factory UserProgress.fromMap(Map<String, dynamic> map) {
    return UserProgress(
      userId: map['userId'] ?? '',
      topicProgress: (map['topicProgress'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, TopicProgress.fromMap(value)),
          ) ??
          {},
      weaknessAnalysis: WeaknessAnalysis.fromMap(map['weaknessAnalysis'] ?? {}),
      totalQuizzesTaken: map['totalQuizzesTaken'] ?? 0,
      totalQuestionsAnswered: map['totalQuestionsAnswered'] ?? 0,
      totalCorrectAnswers: map['totalCorrectAnswers'] ?? 0,
      overallAccuracy: map['overallAccuracy']?.toDouble() ?? 0.0,
      studyStreakDays: map['studyStreakDays'] ?? 0,
      lastStudyDate: DateTime.parse(map['lastStudyDate']),
      totalStudyTimeMinutes: map['totalStudyTimeMinutes'] ?? 0,
      achievements: List<String>.from(map['achievements'] ?? []),
      skillScores: Map<String, double>.from(map['skillScores'] ?? {}),
    );
  }

  UserProgress copyWith({
    String? userId,
    Map<String, TopicProgress>? topicProgress,
    WeaknessAnalysis? weaknessAnalysis,
    int? totalQuizzesTaken,
    int? totalQuestionsAnswered,
    int? totalCorrectAnswers,
    double? overallAccuracy,
    int? studyStreakDays,
    DateTime? lastStudyDate,
    int? totalStudyTimeMinutes,
    List<String>? achievements,
    Map<String, double>? skillScores,
  }) {
    return UserProgress(
      userId: userId ?? this.userId,
      topicProgress: topicProgress ?? this.topicProgress,
      weaknessAnalysis: weaknessAnalysis ?? this.weaknessAnalysis,
      totalQuizzesTaken: totalQuizzesTaken ?? this.totalQuizzesTaken,
      totalQuestionsAnswered: totalQuestionsAnswered ?? this.totalQuestionsAnswered,
      totalCorrectAnswers: totalCorrectAnswers ?? this.totalCorrectAnswers,
      overallAccuracy: overallAccuracy ?? this.overallAccuracy,
      studyStreakDays: studyStreakDays ?? this.studyStreakDays,
      lastStudyDate: lastStudyDate ?? this.lastStudyDate,
      totalStudyTimeMinutes: totalStudyTimeMinutes ?? this.totalStudyTimeMinutes,
      achievements: achievements ?? this.achievements,
      skillScores: skillScores ?? this.skillScores,
    );
  }

  double get accuracyPercentage => overallAccuracy * 100;

  String get studyTimeText {
    final hours = totalStudyTimeMinutes ~/ 60;
    final minutes = totalStudyTimeMinutes % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  int get completedTopics {
    return topicProgress.values.where((progress) => progress.isCompleted).length;
  }

  double get overallProgress {
    if (topicProgress.isEmpty) return 0.0;
    final totalProgress = topicProgress.values
        .map((progress) => progress.progressPercentage)
        .reduce((a, b) => a + b);
    return totalProgress / topicProgress.length;
  }
}

class TopicProgress {
  final String topicId;
  final double progressPercentage;
  final bool isCompleted;
  final int lessonsCompleted;
  final int totalLessons;
  final int quizzesTaken;
  final double averageScore;
  final DateTime lastStudied;
  final int timeSpentMinutes;

  const TopicProgress({
    required this.topicId,
    this.progressPercentage = 0.0,
    this.isCompleted = false,
    this.lessonsCompleted = 0,
    this.totalLessons = 0,
    this.quizzesTaken = 0,
    this.averageScore = 0.0,
    required this.lastStudied,
    this.timeSpentMinutes = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'topicId': topicId,
      'progressPercentage': progressPercentage,
      'isCompleted': isCompleted,
      'lessonsCompleted': lessonsCompleted,
      'totalLessons': totalLessons,
      'quizzesTaken': quizzesTaken,
      'averageScore': averageScore,
      'lastStudied': lastStudied.toIso8601String(),
      'timeSpentMinutes': timeSpentMinutes,
    };
  }

  factory TopicProgress.fromMap(Map<String, dynamic> map) {
    return TopicProgress(
      topicId: map['topicId'] ?? '',
      progressPercentage: map['progressPercentage']?.toDouble() ?? 0.0,
      isCompleted: map['isCompleted'] ?? false,
      lessonsCompleted: map['lessonsCompleted'] ?? 0,
      totalLessons: map['totalLessons'] ?? 0,
      quizzesTaken: map['quizzesTaken'] ?? 0,
      averageScore: map['averageScore']?.toDouble() ?? 0.0,
      lastStudied: DateTime.parse(map['lastStudied']),
      timeSpentMinutes: map['timeSpentMinutes'] ?? 0,
    );
  }

  TopicProgress copyWith({
    String? topicId,
    double? progressPercentage,
    bool? isCompleted,
    int? lessonsCompleted,
    int? totalLessons,
    int? quizzesTaken,
    double? averageScore,
    DateTime? lastStudied,
    int? timeSpentMinutes,
  }) {
    return TopicProgress(
      topicId: topicId ?? this.topicId,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      isCompleted: isCompleted ?? this.isCompleted,
      lessonsCompleted: lessonsCompleted ?? this.lessonsCompleted,
      totalLessons: totalLessons ?? this.totalLessons,
      quizzesTaken: quizzesTaken ?? this.quizzesTaken,
      averageScore: averageScore ?? this.averageScore,
      lastStudied: lastStudied ?? this.lastStudied,
      timeSpentMinutes: timeSpentMinutes ?? this.timeSpentMinutes,
    );
  }
}

# Simple Flutter App

A complete Flutter application demonstrating various features including navigation, state management, local storage, and multiple screens.

## Features

- **Home Screen**: Welcome interface with counter functionality and task management
- **Profile Screen**: User profile management with form validation and local storage
- **Settings Screen**: App preferences with toggles, sliders, and configuration options
- **Navigation**: Seamless navigation between screens using <PERSON><PERSON><PERSON>'s routing system
- **Local Storage**: Persistent data storage using SharedPreferences
- **Material Design**: Modern UI following Material Design 3 principles

## Screens

### Home Screen
- Welcome card with app introduction
- Interactive counter with increment functionality
- Quick task list with add/remove capabilities
- Navigation buttons to other screens
- Floating action button for quick counter increment

### Profile Screen
- User avatar with camera placeholder
- Form fields for name, email, gender, and bio
- Form validation and error handling
- Save/load functionality using local storage
- Loading states and user feedback

### Settings Screen
- Notification preferences (enable/disable, sound, vibration)
- Appearance settings (dark mode, font size)
- Language selection
- About section with app information
- Reset functionality for all settings

## Getting Started

### Prerequisites
- Flutter SDK (3.0.0 or higher)
- Dart SDK
- Android Studio / VS Code with Flutter extensions

### Installation

1. Clone or download this project
2. Navigate to the flutter_app directory
3. Install dependencies:
   ```bash
   flutter pub get
   ```
4. Run the app:
   ```bash
   flutter run
   ```

### Dependencies

- `flutter`: Flutter SDK
- `cupertino_icons`: iOS-style icons
- `shared_preferences`: Local data persistence
- `http`: HTTP client for future API integration

## Project Structure

```
lib/
├── main.dart              # App entry point and routing
├── screens/
│   ├── home_screen.dart   # Home screen with counter and tasks
│   ├── profile_screen.dart # User profile management
│   └── settings_screen.dart # App settings and preferences
├── widgets/               # Reusable widgets (future expansion)
└── models/                # Data models (future expansion)
```

## Contributing

This is a demonstration project. Feel free to fork and modify for your own learning purposes.

## License

This project is open source and available under the MIT License.

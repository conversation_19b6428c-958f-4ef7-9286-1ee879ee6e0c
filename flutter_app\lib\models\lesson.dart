import 'example.dart';

enum LessonType { theory, practice, mixed }

class Lesson {
  final String id;
  final String title;
  final String content;
  final LessonType type;
  final List<String> rules;
  final List<Example> examples;
  final List<String> commonMistakes;
  final List<String> tips;
  final int estimatedTimeMinutes;
  final bool isCompleted;
  final DateTime? completedAt;
  final double? userScore;

  const Lesson({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.rules,
    required this.examples,
    required this.commonMistakes,
    required this.tips,
    required this.estimatedTimeMinutes,
    this.isCompleted = false,
    this.completedAt,
    this.userScore,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.name,
      'rules': rules,
      'examples': examples.map((example) => example.toMap()).toList(),
      'commonMistakes': commonMistakes,
      'tips': tips,
      'estimatedTimeMinutes': estimatedTimeMinutes,
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
      'userScore': userScore,
    };
  }

  factory Lesson.fromMap(Map<String, dynamic> map) {
    return Lesson(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      content: map['content'] ?? '',
      type: LessonType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => LessonType.theory,
      ),
      rules: List<String>.from(map['rules'] ?? []),
      examples: (map['examples'] as List<dynamic>?)
              ?.map((example) => Example.fromMap(example))
              .toList() ??
          [],
      commonMistakes: List<String>.from(map['commonMistakes'] ?? []),
      tips: List<String>.from(map['tips'] ?? []),
      estimatedTimeMinutes: map['estimatedTimeMinutes'] ?? 0,
      isCompleted: map['isCompleted'] ?? false,
      completedAt: map['completedAt'] != null
          ? DateTime.parse(map['completedAt'])
          : null,
      userScore: map['userScore']?.toDouble(),
    );
  }

  Lesson copyWith({
    String? id,
    String? title,
    String? content,
    LessonType? type,
    List<String>? rules,
    List<Example>? examples,
    List<String>? commonMistakes,
    List<String>? tips,
    int? estimatedTimeMinutes,
    bool? isCompleted,
    DateTime? completedAt,
    double? userScore,
  }) {
    return Lesson(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      rules: rules ?? this.rules,
      examples: examples ?? this.examples,
      commonMistakes: commonMistakes ?? this.commonMistakes,
      tips: tips ?? this.tips,
      estimatedTimeMinutes: estimatedTimeMinutes ?? this.estimatedTimeMinutes,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      userScore: userScore ?? this.userScore,
    );
  }

  String get typeText {
    switch (type) {
      case LessonType.theory:
        return 'Theory';
      case LessonType.practice:
        return 'Practice';
      case LessonType.mixed:
        return 'Theory + Practice';
    }
  }

  String get statusText {
    if (isCompleted) {
      return userScore != null ? 'Completed (${userScore!.toInt()}%)' : 'Completed';
    }
    return 'Not Started';
  }
}

enum IELTSQuestionType {
  multipleChoice,
  fillInTheBlank,
  errorCorrection,
  sentenceCompletion,
  matching,
  trueFalse,
  shortAnswer
}

class QuizQuestion {
  final String id;
  final String question;
  final IELTSQuestionType type;
  final List<String> options; // For multiple choice
  final String correctAnswer;
  final List<String> acceptableAnswers; // Alternative correct answers
  final String explanation;
  final String grammarRule;
  final int points;
  final String category; // e.g., "Present Perfect", "Conditionals"
  final String? userAnswer;
  final bool? isCorrect;
  final DateTime? answeredAt;

  const QuizQuestion({
    required this.id,
    required this.question,
    required this.type,
    this.options = const [],
    required this.correctAnswer,
    this.acceptableAnswers = const [],
    required this.explanation,
    required this.grammarRule,
    this.points = 1,
    required this.category,
    this.userAnswer,
    this.isCorrect,
    this.answeredAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'question': question,
      'type': type.name,
      'options': options,
      'correctAnswer': correctAnswer,
      'acceptableAnswers': acceptableAnswers,
      'explanation': explanation,
      'grammarRule': grammarRule,
      'points': points,
      'category': category,
      'userAnswer': userAnswer,
      'isCorrect': isCorrect,
      'answeredAt': answeredAt?.toIso8601String(),
    };
  }

  factory QuizQuestion.fromMap(Map<String, dynamic> map) {
    return QuizQuestion(
      id: map['id'] ?? '',
      question: map['question'] ?? '',
      type: IELTSQuestionType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => IELTSQuestionType.multipleChoice,
      ),
      options: List<String>.from(map['options'] ?? []),
      correctAnswer: map['correctAnswer'] ?? '',
      acceptableAnswers: List<String>.from(map['acceptableAnswers'] ?? []),
      explanation: map['explanation'] ?? '',
      grammarRule: map['grammarRule'] ?? '',
      points: map['points'] ?? 1,
      category: map['category'] ?? '',
      userAnswer: map['userAnswer'],
      isCorrect: map['isCorrect'],
      answeredAt: map['answeredAt'] != null
          ? DateTime.parse(map['answeredAt'])
          : null,
    );
  }

  QuizQuestion copyWith({
    String? id,
    String? question,
    IELTSQuestionType? type,
    List<String>? options,
    String? correctAnswer,
    List<String>? acceptableAnswers,
    String? explanation,
    String? grammarRule,
    int? points,
    String? category,
    String? userAnswer,
    bool? isCorrect,
    DateTime? answeredAt,
  }) {
    return QuizQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      type: type ?? this.type,
      options: options ?? this.options,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      acceptableAnswers: acceptableAnswers ?? this.acceptableAnswers,
      explanation: explanation ?? this.explanation,
      grammarRule: grammarRule ?? this.grammarRule,
      points: points ?? this.points,
      category: category ?? this.category,
      userAnswer: userAnswer ?? this.userAnswer,
      isCorrect: isCorrect ?? this.isCorrect,
      answeredAt: answeredAt ?? this.answeredAt,
    );
  }

  String get typeText {
    switch (type) {
      case IELTSQuestionType.multipleChoice:
        return 'Multiple Choice';
      case IELTSQuestionType.fillInTheBlank:
        return 'Fill in the Blank';
      case IELTSQuestionType.errorCorrection:
        return 'Error Correction';
      case IELTSQuestionType.sentenceCompletion:
        return 'Sentence Completion';
      case IELTSQuestionType.matching:
        return 'Matching';
      case IELTSQuestionType.trueFalse:
        return 'True/False';
      case IELTSQuestionType.shortAnswer:
        return 'Short Answer';
    }
  }

  bool checkAnswer(String answer) {
    final normalizedAnswer = answer.trim().toLowerCase();
    final normalizedCorrect = correctAnswer.trim().toLowerCase();
    
    if (normalizedAnswer == normalizedCorrect) return true;
    
    return acceptableAnswers.any(
      (acceptable) => acceptable.trim().toLowerCase() == normalizedAnswer,
    );
  }

  String get statusText {
    if (userAnswer == null) return 'Not Answered';
    return isCorrect == true ? 'Correct' : 'Incorrect';
  }
}

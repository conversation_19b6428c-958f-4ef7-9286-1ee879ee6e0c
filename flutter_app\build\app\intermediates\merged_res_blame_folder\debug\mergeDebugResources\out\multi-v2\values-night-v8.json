{"logs": [{"outputFile": "com.example.ielts_grammar_master.app-mergeDebugResources-4:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "9,10,11,12,13,14,15,16", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "405,475,559,643,739,841,943,1037", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "470,554,638,734,836,938,1032,1121"}}, {"source": "C:\\Phprojects\\ph12-Assignment02\\flutter_app\\android\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "175,831", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "482,997"}, "to": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,236", "endLines": "5,8", "endColumns": "12,12", "endOffsets": "231,400"}}]}]}
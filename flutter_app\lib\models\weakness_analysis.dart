enum WeaknessLevel { minor, moderate, major, critical }

class WeaknessAnalysis {
  final Map<String, GrammarWeakness> weaknesses;
  final List<String> priorityTopics;
  final Map<String, double> categoryScores;
  final DateTime lastAnalyzed;
  final int totalAnalyses;

  const WeaknessAnalysis({
    this.weaknesses = const {},
    this.priorityTopics = const [],
    this.categoryScores = const {},
    required this.lastAnalyzed,
    this.totalAnalyses = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'weaknesses': weaknesses.map(
        (key, value) => MapEntry(key, value.toMap()),
      ),
      'priorityTopics': priorityTopics,
      'categoryScores': categoryScores,
      'lastAnalyzed': lastAnalyzed.toIso8601String(),
      'totalAnalyses': totalAnalyses,
    };
  }

  factory WeaknessAnalysis.fromMap(Map<String, dynamic> map) {
    return WeaknessAnalysis(
      weaknesses: (map['weaknesses'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, GrammarWeakness.fromMap(value)),
          ) ??
          {},
      priorityTopics: List<String>.from(map['priorityTopics'] ?? []),
      categoryScores: Map<String, double>.from(map['categoryScores'] ?? {}),
      lastAnalyzed: DateTime.parse(map['lastAnalyzed']),
      totalAnalyses: map['totalAnalyses'] ?? 0,
    );
  }

  WeaknessAnalysis copyWith({
    Map<String, GrammarWeakness>? weaknesses,
    List<String>? priorityTopics,
    Map<String, double>? categoryScores,
    DateTime? lastAnalyzed,
    int? totalAnalyses,
  }) {
    return WeaknessAnalysis(
      weaknesses: weaknesses ?? this.weaknesses,
      priorityTopics: priorityTopics ?? this.priorityTopics,
      categoryScores: categoryScores ?? this.categoryScores,
      lastAnalyzed: lastAnalyzed ?? this.lastAnalyzed,
      totalAnalyses: totalAnalyses ?? this.totalAnalyses,
    );
  }

  List<GrammarWeakness> get criticalWeaknesses {
    return weaknesses.values
        .where((weakness) => weakness.level == WeaknessLevel.critical)
        .toList()
      ..sort((a, b) => b.frequency.compareTo(a.frequency));
  }

  List<GrammarWeakness> get majorWeaknesses {
    return weaknesses.values
        .where((weakness) => weakness.level == WeaknessLevel.major)
        .toList()
      ..sort((a, b) => b.frequency.compareTo(a.frequency));
  }

  double get overallWeaknessScore {
    if (weaknesses.isEmpty) return 0.0;
    final totalScore = weaknesses.values
        .map((weakness) => weakness.severityScore)
        .reduce((a, b) => a + b);
    return totalScore / weaknesses.length;
  }

  bool get hasSignificantWeaknesses {
    return criticalWeaknesses.isNotEmpty || majorWeaknesses.length > 2;
  }
}

class GrammarWeakness {
  final String category;
  final String description;
  final WeaknessLevel level;
  final int frequency;
  final double accuracyRate;
  final List<String> commonMistakes;
  final List<String> recommendedLessons;
  final DateTime firstIdentified;
  final DateTime lastOccurrence;
  final bool isImproving;

  const GrammarWeakness({
    required this.category,
    required this.description,
    required this.level,
    required this.frequency,
    required this.accuracyRate,
    required this.commonMistakes,
    required this.recommendedLessons,
    required this.firstIdentified,
    required this.lastOccurrence,
    this.isImproving = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'category': category,
      'description': description,
      'level': level.name,
      'frequency': frequency,
      'accuracyRate': accuracyRate,
      'commonMistakes': commonMistakes,
      'recommendedLessons': recommendedLessons,
      'firstIdentified': firstIdentified.toIso8601String(),
      'lastOccurrence': lastOccurrence.toIso8601String(),
      'isImproving': isImproving,
    };
  }

  factory GrammarWeakness.fromMap(Map<String, dynamic> map) {
    return GrammarWeakness(
      category: map['category'] ?? '',
      description: map['description'] ?? '',
      level: WeaknessLevel.values.firstWhere(
        (level) => level.name == map['level'],
        orElse: () => WeaknessLevel.minor,
      ),
      frequency: map['frequency'] ?? 0,
      accuracyRate: map['accuracyRate']?.toDouble() ?? 0.0,
      commonMistakes: List<String>.from(map['commonMistakes'] ?? []),
      recommendedLessons: List<String>.from(map['recommendedLessons'] ?? []),
      firstIdentified: DateTime.parse(map['firstIdentified']),
      lastOccurrence: DateTime.parse(map['lastOccurrence']),
      isImproving: map['isImproving'] ?? false,
    );
  }

  GrammarWeakness copyWith({
    String? category,
    String? description,
    WeaknessLevel? level,
    int? frequency,
    double? accuracyRate,
    List<String>? commonMistakes,
    List<String>? recommendedLessons,
    DateTime? firstIdentified,
    DateTime? lastOccurrence,
    bool? isImproving,
  }) {
    return GrammarWeakness(
      category: category ?? this.category,
      description: description ?? this.description,
      level: level ?? this.level,
      frequency: frequency ?? this.frequency,
      accuracyRate: accuracyRate ?? this.accuracyRate,
      commonMistakes: commonMistakes ?? this.commonMistakes,
      recommendedLessons: recommendedLessons ?? this.recommendedLessons,
      firstIdentified: firstIdentified ?? this.firstIdentified,
      lastOccurrence: lastOccurrence ?? this.lastOccurrence,
      isImproving: isImproving ?? this.isImproving,
    );
  }

  String get levelText {
    switch (level) {
      case WeaknessLevel.minor:
        return 'Minor';
      case WeaknessLevel.moderate:
        return 'Moderate';
      case WeaknessLevel.major:
        return 'Major';
      case WeaknessLevel.critical:
        return 'Critical';
    }
  }

  double get severityScore {
    switch (level) {
      case WeaknessLevel.minor:
        return 1.0;
      case WeaknessLevel.moderate:
        return 2.0;
      case WeaknessLevel.major:
        return 3.0;
      case WeaknessLevel.critical:
        return 4.0;
    }
  }

  String get accuracyText => '${(accuracyRate * 100).toInt()}%';

  String get improvementText {
    return isImproving ? 'Improving' : 'Needs Focus';
  }
}

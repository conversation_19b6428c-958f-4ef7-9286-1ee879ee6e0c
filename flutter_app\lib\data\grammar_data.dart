import '../models/grammar_topic.dart';
import '../models/lesson.dart';
import '../models/example.dart';

class GrammarData {
  static List<GrammarTopic> getAllTopics() {
    return [
      // I. FOUNDATIONS: The Core Systems
      _createPartsOfSpeech(),
      _createSentenceStructure(),

      // II. VERB SYSTEM: The Engine of Meaning
      _createVerbTenses(),
      _createPassiveVoice(),
      _createModalVerbs(),
      _createNonFiniteVerbs(),

      // III. SENTENCE ARCHITECTURE: Complexity & Clarity
      _createClauseCombining(),
      _createRelativeClauses(),
      _createAdverbialClauses(),
      _createConditionals(),
      _createInversionAndClefts(),

      // IV. AGREEMENT & REFERENCE: Precision & Flow
      _createSubjectVerbAgreement(),
      _createPronounsAndAntecedents(),

      // V. THE NITTY-GRITTY: Nuance & Accuracy
      _createArticlesAndDeterminers(),
      _createQuantifiers(),
      _createPrepositions(),
      _createComparativesSuperlatives(),
      _createDiscourseMarkers(),

      // VI. PUNCTUATION: The Traffic Signals
      _createPunctuation(),
    ];
  }

  static GrammarTopic _createPartsOfSpeech() {
    return GrammarTopic(
      id: 'parts_of_speech',
      title: 'Parts of Speech - The Foundation',
      description: 'Master all 9 parts of speech with comprehensive subcategories: nouns, pronouns, verbs, adjectives, adverbs, prepositions, conjunctions, determiners, and interjections.',
      icon: '🧩',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking, IELTSSkill.reading],
      estimatedTimeMinutes: 90,
      keyPoints: [
        'Nouns: Common, Proper, Countable, Uncountable, Collective, Abstract',
        'Pronouns: Personal, Possessive, Reflexive, Demonstrative, Relative, Interrogative, Indefinite',
        'Verbs: Lexical & Auxiliary (Primary & Modal)',
        'Adjectives: Types, Order, Gradable vs. Non-gradable',
        'Adverbs: Manner, Place, Time, Frequency, Degree, Focus, Viewpoint',
        'Prepositions: Time, Place, Movement, Phrasal Verbs',
        'Conjunctions: Coordinating, Subordinating, Correlative',
        'Determiners: Articles, Quantifiers, Demonstratives, Possessives',
        'Interjections: Express emotions and reactions'
      ],
      lessons: [
        Lesson(
          id: 'comprehensive_nouns',
          title: 'Comprehensive Nouns',
          estimatedTimeMinutes: 30,
          content: '''
**NOUNS** - The Foundation of Language

**A. NOUN TYPES:**

**1. Common vs. Proper Nouns:**
• Common: General categories (teacher, city, book, emotion)
• Proper: Specific names - ALWAYS capitalized (Shakespeare, London, IELTS, Monday)

**2. Countable vs. Uncountable:**
• Countable: Can be counted (one book, two books, many students)
• Uncountable: Cannot be counted (water, information, advice, furniture)
  - Use "much" with uncountable, "many" with countable
  - Some nouns can be both: "coffee" (uncountable) vs. "a coffee" (one cup)

**3. Concrete vs. Abstract:**
• Concrete: Physical things you can touch (table, dog, mountain)
• Abstract: Ideas, emotions, concepts (happiness, democracy, intelligence)

**4. Collective Nouns:**
• Groups treated as single units (team, family, government, staff)
• Can be singular or plural depending on context:
  - "The team IS winning" (as one unit)
  - "The team ARE arguing" (individual members)

**BAND 9 TIP:** Master uncountable nouns - they're often tested in IELTS!
          ''',
          type: LessonType.theory,
          rules: [
            'Proper nouns always start with capital letters',
            'Use pronouns to replace nouns that have already been mentioned',
            'Pronouns must agree with the noun they replace in number and gender',
            'Use "it" for things, animals, and babies when gender is unknown'
          ],
          examples: [
            Example(
              id: 'noun_ex1',
              sentence: 'The student forgot his book. He needs it for class.',
              explanation: '"He" replaces "the student", "it" replaces "book"',
              type: ExampleType.correct,
              highlights: ['student', 'He', 'it'],
            ),
            Example(
              id: 'noun_ex2',
              sentence: 'Sarah is a teacher. She loves her job.',
              explanation: '"She" and "her" refer back to Sarah',
              type: ExampleType.correct,
              highlights: ['Sarah', 'She', 'her'],
            ),
          ],
          commonMistakes: [
            'The student forgot their book (incorrect for singular) → The student forgot his/her book',
            'Me and John went shopping (incorrect) → John and I went shopping'
          ],
          tips: [
            'Always capitalize proper nouns',
            'Use subject pronouns (I, he, she) as subjects',
            'Use object pronouns (me, him, her) as objects'
          ],
        ),
        Lesson(
          id: 'comprehensive_pronouns',
          title: 'Comprehensive Pronouns',
          estimatedTimeMinutes: 25,
          content: '''
**PRONOUNS** - Replace nouns to avoid repetition and create flow

**A. PERSONAL PRONOUNS:**
• Subject: I, you, he, she, it, we, they
• Object: me, you, him, her, it, us, them
• "I saw him" vs. "He saw me"

**B. POSSESSIVE PRONOUNS:**
• Adjective form: my, your, his, her, its, our, their (+ noun)
• Standalone: mine, yours, his, hers, ours, theirs
• "This is MY book" vs. "This book is MINE"

**C. REFLEXIVE PRONOUNS:**
• myself, yourself, himself, herself, itself, ourselves, yourselves, themselves
• Use when subject = object: "I hurt myself"
• For emphasis: "I did it myself"

**D. DEMONSTRATIVE PRONOUNS:**
• Near: this (singular), these (plural)
• Far: that (singular), those (plural)
• "This is good" vs. "These are good"

**E. RELATIVE PRONOUNS:**
• who, whom, whose, which, that, where, when, why
• Connect clauses: "The man WHO called..."

**F. INTERROGATIVE PRONOUNS:**
• who, whom, whose, what, which
• Ask questions: "WHO is coming?"

**G. INDEFINITE PRONOUNS:**
• everyone, someone, anyone, no one, everybody, somebody, anybody, nobody
• everything, something, anything, nothing
• all, both, each, either, neither, few, many, several, some, any

**BAND 9 TIP:** Master "whom" for formal writing - "To whom did you speak?"
          ''',
          type: LessonType.theory,
          rules: [
            'Use subject pronouns (I, he, she) as subjects of sentences',
            'Use object pronouns (me, him, her) as objects of verbs/prepositions',
            'Reflexive pronouns must refer back to the subject',
            'Indefinite pronouns are usually singular (everyone IS, not ARE)'
          ],
          examples: [
            Example(
              id: 'pronoun_ex1',
              sentence: 'She gave the book to him.',
              explanation: '"She" (subject), "him" (object of preposition)',
              type: ExampleType.correct,
              highlights: ['She', 'him'],
            ),
            Example(
              id: 'pronoun_ex2',
              sentence: 'Everyone should bring their own lunch.',
              explanation: 'Singular "they" is acceptable with indefinite pronouns',
              type: ExampleType.correct,
              highlights: ['Everyone', 'their'],
            ),
          ],
          commonMistakes: [
            'Me and John went → John and I went (subject position)',
            'Between you and I → Between you and me (object of preposition)',
            'Everyone forgot their books → Everyone forgot his/her book (formal)'
          ],
          tips: [
            'Test subject/object: "I went" vs. "Me went" - which sounds right?',
            'Put yourself last in compound subjects: "John and I"',
            'After prepositions, use object pronouns: "with me", "for her"'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createSentenceStructure() {
    return GrammarTopic(
      id: 'sentence_structure',
      title: 'Sentence Structure',
      description: 'Learn to build clear, correct sentences with proper subject-predicate relationships and avoid fragments and run-ons.',
      icon: '🏗️',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 45,
      keyPoints: [
        'Every sentence needs a subject and predicate',
        'Simple sentences express one complete idea',
        'Compound sentences join two ideas with conjunctions',
        'Complex sentences have main clauses and dependent clauses',
        'Avoid sentence fragments and run-on sentences'
      ],
      lessons: [
        Lesson(
          id: 'basic_sentence_structure',
          title: 'Basic Sentence Structure',
          estimatedTimeMinutes: 25,
          content: '''
**SENTENCE STRUCTURE** is the foundation of clear communication.

**Subject and Predicate:**
Every sentence needs:
• Subject: Who or what the sentence is about
• Predicate: What the subject does or is

**Types of Sentences:**
1. **Simple**: One idea (e.g., I like tea)
2. **Compound**: Two ideas joined by and, but, etc. (e.g., I like tea, but she likes coffee)
3. **Complex**: One main idea and extra details (e.g., I study because I want to learn)

**Common Problems:**
• Sentence Fragments: Incomplete sentences (e.g., Because I was tired)
• Run-on Sentences: Two sentences joined without punctuation (e.g., I was tired I slept)
          ''',
          type: LessonType.theory,
          rules: [
            'Always include a subject and verb in every sentence',
            'Use coordinating conjunctions (and, but, or, so) to join simple sentences',
            'Use subordinating conjunctions (because, although, when) for complex sentences',
            'Separate independent clauses with proper punctuation'
          ],
          examples: [
            Example(
              id: 'sentence_ex1',
              sentence: 'She studies every day.',
              explanation: 'Simple sentence: Subject (She) + Predicate (studies every day)',
              type: ExampleType.correct,
              highlights: ['She', 'studies every day'],
            ),
            Example(
              id: 'sentence_ex2',
              sentence: 'I like coffee, but she prefers tea.',
              explanation: 'Compound sentence: Two complete ideas joined by "but"',
              type: ExampleType.correct,
              highlights: ['I like coffee', 'she prefers tea'],
            ),
          ],
          commonMistakes: [
            'Because I was tired. (fragment) → I slept because I was tired.',
            'I was tired I slept. (run-on) → I was tired, so I slept.'
          ],
          tips: [
            'Read your sentences aloud to check if they sound complete',
            'Look for missing subjects or verbs',
            'Use commas before coordinating conjunctions in compound sentences'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createVerbTenses() {
    return GrammarTopic(
      id: 'verb_tenses',
      title: 'Verb Tenses',
      description: 'Master all 12 English tenses: present, past, and future with simple, continuous, perfect, and perfect continuous forms.',
      icon: '⏰',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 90,
      keyPoints: [
        'Present tenses: Simple, Continuous, Perfect, Perfect Continuous',
        'Past tenses: Simple, Continuous, Perfect, Perfect Continuous',
        'Future tenses: Simple, Continuous, Perfect, Perfect Continuous',
        'Match tense to time of action',
        'Practice all 12 tenses for IELTS success',
      ],
      lessons: [
        Lesson(
          id: 'present_tenses',
          title: 'Present Tenses',
          content: '''
Present tenses are fundamental to English grammar and crucial for IELTS success. They express actions, states, and habits in the present time.

## Present Simple
Used for:
- Permanent situations: "She lives in London"
- Habits and routines: "I drink coffee every morning"
- General truths: "Water boils at 100°C"
- Scheduled future events: "The train leaves at 8 PM"

## Present Continuous
Used for:
- Actions happening now: "I am writing an essay"
- Temporary situations: "She is staying with friends"
- Future arrangements: "We are meeting tomorrow"
- Changing situations: "The weather is getting warmer"

## Present Perfect
Used for:
- Past actions with present relevance: "I have finished my homework"
- Experiences: "She has visited Paris three times"
- Actions continuing to present: "They have lived here for 10 years"

## Present Perfect Continuous
Used for:
- Actions that started in the past and continue: "I have been studying for 3 hours"
- Recent activities with present evidence: "You look tired. Have you been running?"
''',
          type: LessonType.theory,
          rules: [
            'Present Simple: Subject + base verb (+ s/es for 3rd person)',
            'Present Continuous: Subject + am/is/are + verb-ing',
            'Present Perfect: Subject + have/has + past participle',
            'Present Perfect Continuous: Subject + have/has + been + verb-ing',
          ],
          examples: [
            Example(
              id: 'ex1',
              sentence: 'I work in a bank.',
              explanation: 'Present Simple for permanent job',
              type: ExampleType.correct,
              highlights: ['work'],
            ),
            Example(
              id: 'ex2',
              sentence: 'I am working on a project.',
              explanation: 'Present Continuous for current activity',
              type: ExampleType.correct,
              highlights: ['am working'],
            ),
            Example(
              id: 'ex3',
              sentence: 'I have worked here for 5 years.',
              explanation: 'Present Perfect for duration up to now',
              type: ExampleType.correct,
              highlights: ['have worked'],
            ),
          ],
          commonMistakes: [
            'Using Present Simple instead of Present Perfect: "I work here for 5 years" ❌',
            'Forgetting -s for 3rd person: "She work hard" ❌',
            'Using Present Continuous for permanent states: "I am knowing the answer" ❌',
          ],
          tips: [
            'Use time markers to identify the correct tense',
            'Remember stative verbs (know, like, want) rarely use continuous forms',
            'Present Perfect connects past and present',
          ],
          estimatedTimeMinutes: 30,
        ),
        Lesson(
          id: 'past_tenses',
          title: 'Past Tenses',
          content: '''
Past tenses describe completed actions, states, and habits in the past. Mastering these is essential for IELTS Writing Task 1 (describing trends) and storytelling.

## Past Simple
Used for:
- Completed actions: "I visited Paris last year"
- Past habits: "She always walked to school"
- Sequence of past events: "He opened the door and entered"

## Past Continuous
Used for:
- Actions in progress in the past: "I was reading when you called"
- Background actions: "While it was raining, we stayed inside"
- Interrupted actions: "She was cooking when the phone rang"

## Past Perfect
Used for:
- Actions completed before another past action: "I had finished dinner before she arrived"
- Past experiences before a past time: "By 2010, he had visited 20 countries"

## Past Perfect Continuous
Used for:
- Duration before a past point: "I had been waiting for an hour when the bus came"
- Cause of past situation: "She was tired because she had been working all day"
''',
          type: LessonType.theory,
          rules: [
            'Past Simple: Subject + past form of verb',
            'Past Continuous: Subject + was/were + verb-ing',
            'Past Perfect: Subject + had + past participle',
            'Past Perfect Continuous: Subject + had + been + verb-ing',
          ],
          examples: [
            Example(
              id: 'ex4',
              sentence: 'The company launched the product in 2020.',
              explanation: 'Past Simple for completed action with specific time',
              type: ExampleType.correct,
              highlights: ['launched'],
            ),
            Example(
              id: 'ex5',
              sentence: 'Sales were increasing rapidly during that period.',
              explanation: 'Past Continuous for ongoing past action',
              type: ExampleType.correct,
              highlights: ['were increasing'],
            ),
          ],
          commonMistakes: [
            'Using Past Perfect unnecessarily: "After I had eaten, I had gone to bed" ❌',
            'Confusing Past Simple and Past Continuous: "I studied when you called" ❌',
          ],
          tips: [
            'Use Past Perfect only when showing sequence of past events',
            'Past Continuous sets the scene for Past Simple actions',
            'Time expressions help determine the correct past tense',
          ],
          estimatedTimeMinutes: 30,
        ),
        Lesson(
          id: 'future_forms',
          title: 'Future Forms',
          content: '''
English has several ways to express future time. Each form has specific uses and choosing correctly is crucial for IELTS band 7+.

## Will + Base Verb
Used for:
- Predictions: "It will rain tomorrow"
- Spontaneous decisions: "I'll help you with that"
- Promises: "I will call you later"
- Future facts: "The sun will rise at 6 AM"

## Be Going To
Used for:
- Plans and intentions: "I'm going to study medicine"
- Predictions with evidence: "Look at those clouds! It's going to rain"

## Present Continuous for Future
Used for:
- Fixed arrangements: "I'm meeting John at 3 PM"
- Personal plans: "We're flying to Tokyo next week"

## Present Simple for Future
Used for:
- Timetables: "The train leaves at 8:30"
- Scheduled events: "The conference starts on Monday"

## Future Continuous
Used for:
- Actions in progress at future time: "This time tomorrow, I'll be flying to Paris"
- Polite inquiries: "Will you be using the car tonight?"

## Future Perfect
Used for:
- Completed actions before future time: "I will have finished by 5 PM"
- Achievements by future time: "By 2030, we will have reduced emissions by 50%"
''',
          type: LessonType.theory,
          rules: [
            'Will: Subject + will + base verb',
            'Going to: Subject + am/is/are + going to + base verb',
            'Future Continuous: Subject + will + be + verb-ing',
            'Future Perfect: Subject + will + have + past participle',
          ],
          examples: [
            Example(
              id: 'ex6',
              sentence: 'Renewable energy will become more affordable.',
              explanation: 'Will for future prediction',
              type: ExampleType.correct,
              highlights: ['will become'],
            ),
            Example(
              id: 'ex7',
              sentence: 'The government is going to invest in green technology.',
              explanation: 'Going to for planned future action',
              type: ExampleType.correct,
              highlights: ['is going to invest'],
            ),
          ],
          commonMistakes: [
            'Using will for planned actions: "I will meet John tomorrow" (should be "I\'m meeting")',
            'Using going to for spontaneous decisions: "I\'m going to help you" (should be "I\'ll help")',
          ],
          tips: [
            'Will = spontaneous, going to = planned',
            'Use Present Continuous for definite arrangements',
            'Future Perfect shows completion before a future time',
          ],
          estimatedTimeMinutes: 35,
        ),
      ],
    );
  }

  static GrammarTopic _createConditionals() {
    return GrammarTopic(
      id: 'conditionals',
      title: 'Conditional Sentences',
      description: 'Master all types of conditional sentences for advanced IELTS writing',
      icon: '🔀',
      difficulty: DifficultyLevel.advanced,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 90,
      keyPoints: [
        'Zero, First, Second, and Third Conditionals',
        'Mixed Conditionals',
        'Alternative conditional structures',
        'Conditional expressions in formal writing',
      ],
      lessons: [
        Lesson(
          id: 'basic_conditionals',
          title: 'Basic Conditional Types',
          content: '''
Conditional sentences express hypothetical situations and their consequences. They are essential for IELTS Writing Task 2 arguments and Speaking Part 3 discussions.

## Zero Conditional (General Truths)
Structure: If + Present Simple, Present Simple
Used for: Scientific facts, general truths, habits
Example: "If you heat water to 100°C, it boils."

## First Conditional (Real Future Possibility)
Structure: If + Present Simple, will + base verb
Used for: Likely future situations
Example: "If it rains tomorrow, we will cancel the picnic."

## Second Conditional (Unreal Present)
Structure: If + Past Simple, would + base verb
Used for: Hypothetical present situations
Example: "If I had more time, I would learn Spanish."

## Third Conditional (Unreal Past)
Structure: If + Past Perfect, would have + past participle
Used for: Hypothetical past situations
Example: "If I had studied harder, I would have passed the exam."
''',
          type: LessonType.theory,
          rules: [
            'Zero: If + present, present (facts)',
            'First: If + present, will + verb (likely future)',
            'Second: If + past, would + verb (unreal present)',
            'Third: If + past perfect, would have + past participle (unreal past)',
          ],
          examples: [
            Example(
              id: 'cond1',
              sentence: 'If governments invest in renewable energy, carbon emissions will decrease.',
              explanation: 'First conditional for likely future outcome',
              type: ExampleType.correct,
              highlights: ['If governments invest', 'will decrease'],
            ),
            Example(
              id: 'cond2',
              sentence: 'If I were the president, I would implement stricter environmental policies.',
              explanation: 'Second conditional for hypothetical present situation',
              type: ExampleType.correct,
              highlights: ['If I were', 'would implement'],
            ),
          ],
          commonMistakes: [
            'Using will in if-clause: "If it will rain, we will stay home" ❌',
            'Wrong tense combination: "If I would have time, I will help" ❌',
            'Using was instead of were: "If I was rich..." ❌ (should be "If I were rich...")',
          ],
          tips: [
            'Never use will/would in the if-clause',
            'Use "were" for all persons in second conditional',
            'Third conditional expresses regret about the past',
          ],
          estimatedTimeMinutes: 25,
        ),
      ],
    );
  }

  static GrammarTopic _createPassiveVoice() {
    return GrammarTopic(
      id: 'passive_voice',
      title: 'Passive Voice',
      description: 'Learn to use passive voice effectively for formal IELTS writing',
      icon: '🔄',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.reading],
      estimatedTimeMinutes: 75,
      keyPoints: [
        'Formation of passive voice',
        'When to use passive voice',
        'Passive voice in different tenses',
        'Formal writing applications',
      ],
      lessons: [
        Lesson(
          id: 'passive_formation',
          title: 'Passive Voice Formation',
          content: '''
Passive voice is crucial for IELTS Writing, especially in Task 1 (processes) and formal Task 2 essays. It shifts focus from the doer to the action or result.

## Basic Formation
Active: Subject + Verb + Object
Passive: Object + be + Past Participle (+ by + Subject)

Example:
Active: "The company produces cars."
Passive: "Cars are produced by the company."

## When to Use Passive Voice
1. When the doer is unknown: "My bike was stolen."
2. When the doer is obvious: "The criminal was arrested."
3. When the action is more important: "The bridge was built in 1995."
4. In formal/scientific writing: "The experiment was conducted carefully."
5. To avoid responsibility: "Mistakes were made."

## Passive in Different Tenses
- Present Simple: "English is spoken worldwide."
- Past Simple: "The building was constructed in 1980."
- Present Perfect: "The report has been completed."
- Future: "The project will be finished next month."
- Modal verbs: "The problem can be solved."
''',
          type: LessonType.theory,
          rules: [
            'Passive = be + past participle',
            'Tense is shown in the auxiliary verb "be"',
            'Use "by" to mention the agent (doer)',
            'Object of active sentence becomes subject of passive',
          ],
          examples: [
            Example(
              id: 'pass1',
              sentence: 'Renewable energy sources are being developed rapidly.',
              explanation: 'Present Continuous passive for ongoing development',
              type: ExampleType.correct,
              highlights: ['are being developed'],
            ),
            Example(
              id: 'pass2',
              sentence: 'The research will be published next year.',
              explanation: 'Future passive for planned action',
              type: ExampleType.correct,
              highlights: ['will be published'],
            ),
          ],
          commonMistakes: [
            'Missing "be": "The house built in 1990" ❌ (should be "was built")',
            'Wrong past participle: "The letter was wrote" ❌ (should be "written")',
            'Unnecessary passive: "I was born by my mother" ❌ (just "I was born")',
          ],
          tips: [
            'Use passive when the action is more important than the doer',
            'Passive voice is common in academic and formal writing',
            'Not all verbs can be passive (intransitive verbs cannot)',
          ],
          estimatedTimeMinutes: 25,
        ),
      ],
    );
  }

  static GrammarTopic _createModalVerbs() {
    return GrammarTopic(
      id: 'modal_verbs',
      title: 'Modal Verbs',
      description: 'Express possibility, necessity, and advice with modal verbs',
      icon: '🎯',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 85,
      keyPoints: [
        'Modals of possibility and probability',
        'Modals of necessity and obligation',
        'Modals of advice and suggestion',
        'Past modals and speculation',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createArticles() {
    return GrammarTopic(
      id: 'articles',
      title: 'Articles (A, An, The)',
      description: 'Master the correct use of definite and indefinite articles',
      icon: '📝',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 60,
      keyPoints: [
        'Definite article "the"',
        'Indefinite articles "a" and "an"',
        'Zero article usage',
        'Articles with countable and uncountable nouns',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createPrepositions() {
    return GrammarTopic(
      id: 'prepositions',
      title: 'Prepositions',
      description: 'Learn prepositions of time, place, and movement',
      icon: '🗺️',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 70,
      keyPoints: [
        'Prepositions of time (at, in, on)',
        'Prepositions of place and direction',
        'Dependent prepositions',
        'Phrasal verbs with prepositions',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createPronounsAndAntecedents() {
    return GrammarTopic(
      id: 'pronouns_antecedents',
      title: 'Pronouns and Antecedents',
      description: 'Learn to use pronouns correctly and ensure they match their antecedents in number, gender, and clarity.',
      icon: '🔗',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 40,
      keyPoints: [
        'Pronouns must agree with their antecedents',
        'Use correct pronoun forms (I/me, he/him, she/her)',
        'Ensure pronoun reference is clear',
        'Avoid ambiguous pronoun references',
      ],
      lessons: [
        Lesson(
          id: 'pronoun_agreement',
          title: 'Pronoun Agreement',
          estimatedTimeMinutes: 20,
          content: '''
**PRONOUN-ANTECEDENT AGREEMENT**

Pronouns must match the noun they refer to:
• Agreement: Use the right pronoun for the noun (e.g., The girl lost her book, not his book)
• Correct Form: Use I, me, my, etc., depending on the sentence (e.g., She gave me a pen)
• Clarity: Make sure it's clear who the pronoun refers to

**Common Issues:**
• Tom and Sam went home, and he was tired (unclear who "he" is)
• Everyone should bring their book (everyone is singular, use his/her)
          ''',
          type: LessonType.theory,
          rules: [
            'Match pronouns to the noun in number (singular/plural)',
            'Match pronouns to the noun in gender when known',
            'Use subject pronouns (I, he, she) as subjects',
            'Use object pronouns (me, him, her) as objects',
          ],
          examples: [
            Example(
              id: 'pronoun_ex1',
              sentence: 'The girl lost her book.',
              explanation: '"Her" correctly refers to "the girl"',
              type: ExampleType.correct,
              highlights: ['girl', 'her'],
            ),
          ],
          commonMistakes: [
            'Everyone forgot their book → Everyone forgot his/her book',
            'Me and John went → John and I went'
          ],
          tips: [
            'Always identify what noun the pronoun replaces',
            'Check that the pronoun matches in number and gender',
            'Rewrite sentences if pronoun reference is unclear'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createArticlesAndDeterminers() {
    return GrammarTopic(
      id: 'articles_determiners',
      title: 'Articles and Determiners',
      description: 'Master the use of a, an, the and other determiners to specify nouns correctly.',
      icon: '📝',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 35,
      keyPoints: [
        'Use "the" for specific things',
        'Use "a/an" for non-specific things',
        'Use "a" before consonants, "an" before vowels',
        'No article for general ideas',
      ],
      lessons: [
        Lesson(
          id: 'articles_basics',
          title: 'Articles Basics',
          estimatedTimeMinutes: 20,
          content: '''
**ARTICLES** tell if a noun is specific or general.

• **The**: For specific things (e.g., The sun is bright)
• **A/An**: For non-specific things (e.g., A dog barked – any dog)
• **No Article**: For general ideas (e.g., Dogs bark)

**Rules:**
• Use "a" before consonant sounds
• Use "an" before vowel sounds
• Use "the" when it's clear what you mean
          ''',
          type: LessonType.theory,
          rules: [
            'Use "a" before consonant sounds (a book, a university)',
            'Use "an" before vowel sounds (an apple, an hour)',
            'Use "the" for specific, known items',
            'No article for general concepts or plural nouns in general'
          ],
          examples: [
            Example(
              id: 'article_ex1',
              sentence: 'I saw a dog in the park.',
              explanation: '"A dog" (any dog), "the park" (specific park)',
              type: ExampleType.correct,
              highlights: ['a', 'the'],
            ),
          ],
          commonMistakes: [
            'I like the dogs → I like dogs (general)',
            'She is a teacher at the university → She is a teacher at university'
          ],
          tips: [
            'Listen to the sound, not the letter (an hour, a university)',
            'Use "the" when both speaker and listener know which one',
            'No article with general plural nouns'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createAdjectivesAndAdverbs() {
    return GrammarTopic(
      id: 'adjectives_adverbs',
      title: 'Adjectives and Adverbs',
      description: 'Learn to use adjectives and adverbs correctly to add details and make comparisons.',
      icon: '🎨',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 40,
      keyPoints: [
        'Adjectives describe nouns and pronouns',
        'Adverbs describe verbs, adjectives, or other adverbs',
        'Use -er or more for comparisons',
        'Don\'t confuse adjectives and adverbs',
      ],
      lessons: [
        Lesson(
          id: 'adj_adv_basics',
          title: 'Adjectives vs Adverbs',
          estimatedTimeMinutes: 20,
          content: '''
**ADJECTIVES** describe nouns (e.g., a tall tree)
**ADVERBS** describe verbs or adjectives (e.g., She runs fast)

**Comparisons:**
• Use -er or more for comparing (e.g., taller, more interesting)
• Use -est or most for superlatives (e.g., tallest, most interesting)

**Common Mistake:**
Don't confuse adjectives and adverbs (e.g., She is quiet, not She is quietly)
          ''',
          type: LessonType.theory,
          rules: [
            'Adjectives modify nouns: "The quick brown fox"',
            'Adverbs modify verbs: "He runs quickly"',
            'Many adverbs end in -ly (quickly, carefully)',
            'Some words can be both: "fast car" (adj), "runs fast" (adv)'
          ],
          examples: [
            Example(
              id: 'adj_ex1',
              sentence: 'She is a careful driver.',
              explanation: '"Careful" is an adjective describing "driver"',
              type: ExampleType.correct,
              highlights: ['careful'],
            ),
          ],
          commonMistakes: [
            'She drives careful → She drives carefully',
            'He is very good in English → He is very good at English'
          ],
          tips: [
            'Ask "What kind?" for adjectives, "How?" for adverbs',
            'Most adverbs are formed by adding -ly to adjectives',
            'Some adjectives and adverbs have the same form (fast, hard, late)'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createNonFiniteVerbs() {
    return GrammarTopic(
      id: 'non_finite_verbs',
      title: 'Non-Finite Verbs (Verbals)',
      description: 'Master infinitives, gerunds, and participles to add layers of meaning and achieve Band 9 complexity.',
      icon: '🔄',
      difficulty: DifficultyLevel.advanced,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 60,
      keyPoints: [
        'Infinitives: to + verb (purpose, result, after adjectives)',
        'Gerunds: verb + ing as noun (subject, object, after prepositions)',
        'Present Participles: verb + ing as adjective/adverb (active meaning)',
        'Past Participles: verb + ed/V3 as adjective/adverb (passive meaning)',
        'Perfect infinitives and gerunds for advanced structures'
      ],
      lessons: [
        Lesson(
          id: 'infinitives_gerunds',
          title: 'Infinitives and Gerunds',
          estimatedTimeMinutes: 30,
          content: '''
**INFINITIVES (to + verb)** and **GERUNDS (verb + ing as noun)**

**A. INFINITIVES - Uses:**
1. **Purpose:** "I study to improve my English"
2. **After adjectives:** "It's important to practice"
3. **After certain verbs:** decide, want, expect, plan, hope, need
4. **As subject:** "To learn English is challenging"

**B. GERUNDS - Uses:**
1. **As subject:** "Learning English is fun"
2. **As object:** "I enjoy studying"
3. **After prepositions:** "I'm interested in learning"
4. **After certain verbs:** enjoy, avoid, suggest, finish, mind

**C. VERB PATTERNS:**
• Verb + Infinitive: want, decide, plan, hope, expect, need
• Verb + Gerund: enjoy, avoid, suggest, finish, mind, consider
• Verb + Both: start, begin, continue, like, love, hate

**BAND 9 STRUCTURES:**
• Perfect Infinitive: "He seems to have finished" (past action)
• Perfect Gerund: "I regret having said that" (past action)
• Passive Infinitive: "The work needs to be done"
• Passive Gerund: "I don't mind being helped"
          ''',
          type: LessonType.theory,
          rules: [
            'Use infinitive after adjectives: "happy to see", "difficult to understand"',
            'Use gerund after prepositions: "interested in", "good at"',
            'Some verbs change meaning: "stop to smoke" vs. "stop smoking"',
            'Perfect forms show earlier time: "to have done", "having done"'
          ],
          examples: [
            Example(
              id: 'infinitive_ex1',
              sentence: 'I decided to study abroad.',
              explanation: 'Infinitive after "decide"',
              type: ExampleType.correct,
              highlights: ['to study'],
            ),
            Example(
              id: 'gerund_ex1',
              sentence: 'I enjoy learning new languages.',
              explanation: 'Gerund after "enjoy"',
              type: ExampleType.correct,
              highlights: ['learning'],
            ),
          ],
          commonMistakes: [
            'I enjoy to learn → I enjoy learning',
            'I decided learning → I decided to learn',
            'I\'m interested to learn → I\'m interested in learning'
          ],
          tips: [
            'Learn verb patterns by heart - they\'re crucial for IELTS',
            'After prepositions, always use gerunds, never infinitives',
            'Use perfect forms to show sequence of time'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createClauseCombining() {
    return GrammarTopic(
      id: 'clause_combining',
      title: 'Clause Combining',
      description: 'Master coordination and subordination to create complex, sophisticated sentences for Band 9 writing.',
      icon: '🔗',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 45,
      keyPoints: [
        'Coordination: Linking independent clauses with FANBOYS',
        'Subordination: Linking dependent to independent clauses',
        'Variety in sentence structure for higher band scores',
        'Proper punctuation with coordinating and subordinating conjunctions'
      ],
      lessons: [
        Lesson(
          id: 'coordination_subordination',
          title: 'Coordination and Subordination',
          estimatedTimeMinutes: 25,
          content: '''
**CLAUSE COMBINING** - Creating Complex Sentences

**A. COORDINATION (Equal Ideas):**
Use FANBOYS: For, And, Nor, But, Or, Yet, So
• "I studied hard, AND I passed the exam"
• "It was raining, BUT we went out"
• "She was tired, SO she went to bed"

**B. SUBORDINATION (Unequal Ideas):**
Main clause + Dependent clause with subordinating conjunctions:

**Time:** when, while, before, after, since, until, as soon as
• "WHEN I arrived, everyone was waiting"

**Reason:** because, since, as
• "I stayed home BECAUSE I was sick"

**Contrast:** although, though, even though, while, whereas
• "ALTHOUGH it was expensive, I bought it"

**Condition:** if, unless, provided that, as long as
• "IF you study hard, you will succeed"

**Purpose:** so that, in order that
• "I saved money SO THAT I could travel"

**BAND 9 TIP:** Vary your sentence beginnings - start with dependent clauses sometimes!
          ''',
          type: LessonType.theory,
          rules: [
            'Use comma before coordinating conjunctions in compound sentences',
            'Use comma after dependent clauses that start sentences',
            'Don\'t use comma before "because" in the middle of sentences',
            'Vary sentence structure for higher band scores'
          ],
          examples: [
            Example(
              id: 'coord_ex1',
              sentence: 'I wanted to go, but it was too late.',
              explanation: 'Coordination with "but" - comma before conjunction',
              type: ExampleType.correct,
              highlights: ['but'],
            ),
            Example(
              id: 'subord_ex1',
              sentence: 'Although I was tired, I continued working.',
              explanation: 'Subordination - dependent clause first, comma after',
              type: ExampleType.correct,
              highlights: ['Although', ','],
            ),
          ],
          commonMistakes: [
            'Although I was tired, but I continued → Although I was tired, I continued',
            'I was tired I slept → I was tired, so I slept',
            'Because I was sick I stayed home → Because I was sick, I stayed home'
          ],
          tips: [
            'Don\'t use "but" after "although" - choose one!',
            'Start some sentences with dependent clauses for variety',
            'Use subordination to show relationships between ideas clearly'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createAdverbialClauses() {
    return GrammarTopic(
      id: 'adverbial_clauses',
      title: 'Adverbial Clauses',
      description: 'Express time, place, manner, reason, purpose, result, condition, and concession with sophisticated adverbial clauses.',
      icon: '⏰',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 50,
      keyPoints: [
        'Time clauses: when, while, before, after, since, until',
        'Reason clauses: because, since, as',
        'Purpose clauses: so that, in order that',
        'Result clauses: so...that, such...that',
        'Condition clauses: if, unless, provided that',
        'Concession clauses: although, though, even though'
      ],
      lessons: [
        Lesson(
          id: 'adverbial_types',
          title: 'Types of Adverbial Clauses',
          estimatedTimeMinutes: 30,
          content: '''
**ADVERBIAL CLAUSES** - Add depth and sophistication

**A. TIME CLAUSES:**
• when, while, before, after, since, until, as soon as, whenever
• "WHEN I finish work, I'll call you"
• "WHILE she was studying, I cooked dinner"

**B. PLACE CLAUSES:**
• where, wherever
• "I'll go WHERE you go"

**C. MANNER CLAUSES:**
• as, as if, as though, like
• "She acted AS IF nothing had happened"

**D. REASON CLAUSES:**
• because, since, as
• "I stayed home BECAUSE I was sick"

**E. PURPOSE CLAUSES:**
• so that, in order that
• "I study hard SO THAT I can pass the exam"

**F. RESULT CLAUSES:**
• so...that, such...that
• "It was SO cold THAT the lake froze"
• "It was SUCH a cold day THAT we stayed inside"

**G. CONDITION CLAUSES:**
• if, unless, provided that, as long as, in case
• "IF it rains, we'll stay home"
• "UNLESS you hurry, you'll be late"

**H. CONCESSION CLAUSES:**
• although, though, even though, while, whereas
• "ALTHOUGH it was expensive, I bought it"

**BAND 9 TIP:** Use "such...that" and "so...that" for emphasis and sophistication!
          ''',
          type: LessonType.theory,
          rules: [
            'Use comma when adverbial clause comes first',
            'No comma when adverbial clause comes second (usually)',
            'Use present tense in time clauses for future meaning',
            '"So...that" + adjective/adverb, "such...that" + noun phrase'
          ],
          examples: [
            Example(
              id: 'adv_ex1',
              sentence: 'When I graduate, I will look for a job.',
              explanation: 'Time clause first - comma after, present tense for future',
              type: ExampleType.correct,
              highlights: ['When', 'graduate', ','],
            ),
            Example(
              id: 'adv_ex2',
              sentence: 'The exam was so difficult that many students failed.',
              explanation: 'Result clause with "so...that"',
              type: ExampleType.correct,
              highlights: ['so difficult that'],
            ),
          ],
          commonMistakes: [
            'When I will graduate → When I graduate (no future in time clauses)',
            'It was so a difficult exam → It was such a difficult exam',
            'Although expensive but I bought it → Although expensive, I bought it'
          ],
          tips: [
            'Never use future tense in time clauses - use present for future meaning',
            'Remember: "so + adjective/adverb + that", "such + noun phrase + that"',
            'Use adverbial clauses to show clear relationships between ideas'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createInversionAndClefts() {
    return GrammarTopic(
      id: 'inversion_clefts',
      title: 'Inversion and Cleft Sentences',
      description: 'Master advanced structures for emphasis and formality - essential for Band 8-9 writing.',
      icon: '🔄',
      difficulty: DifficultyLevel.advanced,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.reading],
      estimatedTimeMinutes: 40,
      keyPoints: [
        'Inversion after negative adverbials for emphasis',
        'Inversion in conditionals for formality',
        'It-clefts for focus and emphasis',
        'Wh-clefts for highlighting information',
        'Common in academic and formal writing'
      ],
      lessons: [
        Lesson(
          id: 'inversion_clefts_advanced',
          title: 'Advanced Inversion and Clefts',
          estimatedTimeMinutes: 25,
          content: '''
**INVERSION AND CLEFT SENTENCES** - Band 9 Sophistication

**A. INVERSION AFTER NEGATIVE ADVERBIALS:**
• Never, Seldom, Rarely, Hardly, Scarcely, Little, Not only...but also
• "NEVER have I seen such a beautiful sunset"
• "NOT ONLY did he arrive late, BUT he also forgot his presentation"
• "SELDOM do we see such dedication"

**B. INVERSION IN CONDITIONALS:**
• Had, Were, Should (formal alternatives to if)
• "HAD I known earlier, I would have helped" (= If I had known)
• "WERE she to apply, she would get the job" (= If she were to apply)
• "SHOULD you need help, please call me" (= If you should need help)

**C. IT-CLEFT SENTENCES:**
Focus on specific information: It + be + focus + who/that/when/where...
• "IT WAS THE MANAGER who signed the contract" (not someone else)
• "IT WAS YESTERDAY that I met her" (not another day)
• "IT IS BECAUSE OF THE WEATHER that we cancelled" (reason focus)

**D. WH-CLEFT SENTENCES:**
What/Where/When/Why/How + clause + be + focus
• "WHAT SURPRISED ME was his reaction"
• "WHERE I WANT TO GO is Australia"
• "WHAT WE NEED is more time"

**BAND 9 TIP:** Use these structures sparingly but effectively in Task 2 essays for sophistication!
          ''',
          type: LessonType.theory,
          rules: [
            'Inversion requires auxiliary verb + subject + main verb',
            'Use "do/does/did" if no auxiliary verb exists',
            'It-clefts emphasize the focused element',
            'Wh-clefts put emphasis at the end of the sentence'
          ],
          examples: [
            Example(
              id: 'inversion_ex1',
              sentence: 'Never have I experienced such kindness.',
              explanation: 'Inversion after "never" - auxiliary + subject + main verb',
              type: ExampleType.correct,
              highlights: ['Never have I'],
            ),
            Example(
              id: 'cleft_ex1',
              sentence: 'It was the teacher who inspired me most.',
              explanation: 'It-cleft focusing on "the teacher"',
              type: ExampleType.correct,
              highlights: ['It was', 'who'],
            ),
          ],
          commonMistakes: [
            'Never I have seen → Never have I seen',
            'It was the teacher inspired me → It was the teacher who inspired me',
            'What I want is to travel → What I want is travel (need "to")'
          ],
          tips: [
            'Use inversion for dramatic effect and formality',
            'Practice the word order - it\'s the opposite of normal sentences',
            'Cleft sentences are great for IELTS Task 2 emphasis'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createQuantifiers() {
    return GrammarTopic(
      id: 'quantifiers',
      title: 'Quantifiers',
      description: 'Master quantifiers to express amounts precisely: some, any, much, many, few, little, all, most, both, neither, either.',
      icon: '📊',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 35,
      keyPoints: [
        'Much/many with countable and uncountable nouns',
        'Few/little and a few/a little distinctions',
        'Some/any in positive, negative, and question contexts',
        'All, most, both, neither, either usage',
        'Quantifiers with of + determiner + noun'
      ],
      lessons: [
        Lesson(
          id: 'quantifier_usage',
          title: 'Quantifier Usage',
          estimatedTimeMinutes: 20,
          content: '''
**QUANTIFIERS** - Express amounts and quantities precisely

**A. WITH COUNTABLE NOUNS:**
• many, few, a few, several, both, neither, either
• "MANY students", "A FEW books", "SEVERAL options"

**B. WITH UNCOUNTABLE NOUNS:**
• much, little, a little
• "MUCH time", "LITTLE money", "A LITTLE help"

**C. WITH BOTH:**
• some, any, all, most, no, a lot of, lots of, plenty of
• "SOME water/books", "ANY advice/questions"

**D. POSITIVE vs. NEGATIVE MEANINGS:**
• Few/Little = not many/much (negative feeling)
• A few/A little = some (positive feeling)
• "I have FEW friends" (sad - not many)
• "I have A FEW friends" (happy - some good friends)

**E. SOME vs. ANY:**
• Some: positive statements, offers, requests
• Any: negative statements, questions, conditionals
• "I have SOME money" / "Do you have ANY money?"
• "Would you like SOME coffee?" (offer)

**F. ALL, MOST, BOTH, NEITHER, EITHER:**
• All students (100%), Most students (majority)
• Both books (two), Neither book (not one or the other)
• Either book (one or the other)

**BAND 9 TIP:** Use "a great deal of" (uncountable) and "a great many" (countable) for sophistication!
          ''',
          type: LessonType.theory,
          rules: [
            'Use "much/many" mainly in questions and negatives',
            'Use "a lot of/lots of" in positive statements instead of much/many',
            'Few/little = negative meaning, a few/a little = positive meaning',
            'Some in offers and requests, any in questions and negatives'
          ],
          examples: [
            Example(
              id: 'quant_ex1',
              sentence: 'I don\'t have much time, but I have a little money.',
              explanation: '"Much" with uncountable "time", "a little" with uncountable "money"',
              type: ExampleType.correct,
              highlights: ['much time', 'a little money'],
            ),
            Example(
              id: 'quant_ex2',
              sentence: 'Few people understand, but a few are trying to learn.',
              explanation: '"Few" = not many (negative), "a few" = some (positive)',
              type: ExampleType.correct,
              highlights: ['Few people', 'a few'],
            ),
          ],
          commonMistakes: [
            'I have much books → I have many books (countable)',
            'I have many money → I have much money (uncountable)',
            'I have few good friends → I have a few good friends (positive meaning)'
          ],
          tips: [
            'Learn which nouns are countable/uncountable - it affects quantifier choice',
            'Remember: few/little = negative feeling, a few/a little = positive',
            'Use "some" in offers: "Would you like some help?"'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createComparativesSuperlatives() {
    return GrammarTopic(
      id: 'comparatives_superlatives',
      title: 'Comparatives and Superlatives',
      description: 'Master all forms of comparison: regular, irregular, and advanced structures like "the...the" comparatives.',
      icon: '📈',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 40,
      keyPoints: [
        'Regular forms: -er/-est, more/most',
        'Irregular forms: good/better/best, bad/worse/worst',
        'Advanced structures: as...as, not so...as, the...the',
        'Comparative and superlative with adverbs',
        'Less/least for opposite comparisons'
      ],
      lessons: [
        Lesson(
          id: 'comparison_structures',
          title: 'Comparison Structures',
          estimatedTimeMinutes: 25,
          content: '''
**COMPARATIVES AND SUPERLATIVES** - Express degrees of difference

**A. REGULAR FORMS:**

**Short adjectives (1-2 syllables):**
• Comparative: adjective + -er + than
• Superlative: the + adjective + -est
• "tall → taller → tallest"
• "happy → happier → happiest" (y → i)

**Long adjectives (3+ syllables):**
• Comparative: more + adjective + than
• Superlative: the most + adjective
• "beautiful → more beautiful → most beautiful"

**B. IRREGULAR FORMS:**
• good → better → best
• bad → worse → worst
• far → farther/further → farthest/furthest
• little → less → least
• many/much → more → most

**C. ADVANCED STRUCTURES:**

**Equal comparison:**
• as + adjective + as: "She is AS tall AS her brother"
• not so/as + adjective + as: "It's NOT SO expensive AS I thought"

**Double comparatives:**
• the + comparative, the + comparative
• "THE more you practice, THE better you become"
• "THE sooner, THE better"

**D. WITH ADVERBS:**
• quickly → more quickly → most quickly
• fast → faster → fastest (same form as adjective)
• well → better → best
• badly → worse → worst

**BAND 9 TIP:** Use "far/much/considerably + comparative" for emphasis: "far better", "much more interesting"!
          ''',
          type: LessonType.theory,
          rules: [
            'One-syllable adjectives usually take -er/-est',
            'Three+ syllable adjectives use more/most',
            'Two-syllable adjectives vary - learn common patterns',
            'Use "than" with comparatives, "the" with superlatives'
          ],
          examples: [
            Example(
              id: 'comp_ex1',
              sentence: 'This book is more interesting than that one.',
              explanation: 'Long adjective uses "more...than"',
              type: ExampleType.correct,
              highlights: ['more interesting than'],
            ),
            Example(
              id: 'comp_ex2',
              sentence: 'The harder you work, the more successful you become.',
              explanation: 'Double comparative structure',
              type: ExampleType.correct,
              highlights: ['The harder', 'the more successful'],
            ),
          ],
          commonMistakes: [
            'more better → better (don\'t use both)',
            'most tallest → tallest (don\'t use both)',
            'as tall than → as tall as (use "as...as")'
          ],
          tips: [
            'Never use "more" with -er or "most" with -est',
            'Learn irregular forms by heart - they\'re very common',
            'Use double comparatives for sophisticated cause-effect relationships'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createDiscourseMarkers() {
    return GrammarTopic(
      id: 'discourse_markers',
      title: 'Discourse Markers and Linking Words',
      description: 'Master linking words for coherence and cohesion - vital for IELTS Writing and Speaking Band 9.',
      icon: '🔗',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 50,
      keyPoints: [
        'Addition: furthermore, moreover, in addition, besides',
        'Contrast: however, nevertheless, on the other hand, conversely',
        'Cause & Effect: therefore, consequently, thus, hence, as a result',
        'Purpose: in order to, so as to, so that',
        'Sequence: firstly, subsequently, meanwhile, finally',
        'Exemplification: for example, for instance, such as',
        'Concession: although, despite, in spite of',
        'Summary: in conclusion, to sum up, overall'
      ],
      lessons: [
        Lesson(
          id: 'linking_words_comprehensive',
          title: 'Comprehensive Linking Words',
          estimatedTimeMinutes: 30,
          content: '''
**DISCOURSE MARKERS** - Create flow and coherence (CRITICAL for IELTS!)

**A. ADDITION (Adding information):**
• furthermore, moreover, in addition, besides, what's more
• additionally, also, as well as, not only...but also
• "The course is expensive. FURTHERMORE, it requires a lot of time."

**B. CONTRAST (Showing differences):**
• however, nevertheless, nonetheless, on the other hand
• conversely, in contrast, whereas, while, although
• "The weather was terrible. HOWEVER, we enjoyed our trip."

**C. CAUSE & EFFECT (Showing results):**
• therefore, consequently, thus, hence, as a result
• accordingly, for this reason, due to, because of
• "It rained heavily. CONSEQUENTLY, the match was cancelled."

**D. PURPOSE (Showing intention):**
• in order to, so as to, so that, with the aim of
• "I study hard IN ORDER TO pass the exam."

**E. SEQUENCE/ORDER (Organizing ideas):**
• firstly, secondly, then, next, subsequently
• meanwhile, simultaneously, finally, lastly
• "FIRSTLY, we need to analyze the problem. THEN, we can find solutions."

**F. EXEMPLIFICATION (Giving examples):**
• for example, for instance, such as, namely
• to illustrate, as an illustration, in particular
• "Many countries face this problem, FOR INSTANCE, Japan and Germany."

**G. CONCESSION (Admitting opposite points):**
• although, though, even though, despite, in spite of
• admittedly, granted, while it is true that
• "ALTHOUGH the task was difficult, she completed it successfully."

**H. SUMMARY/CONCLUSION:**
• in conclusion, to sum up, overall, in summary
• all in all, on the whole, to conclude
• "IN CONCLUSION, the benefits outweigh the disadvantages."

**BAND 9 TIP:** Use a WIDE RANGE naturally - avoid overusing basic ones (and, but, so, because)!
          ''',
          type: LessonType.theory,
          rules: [
            'Use commas after most discourse markers at sentence beginnings',
            'Some can be used mid-sentence with commas before and after',
            'Don\'t overuse - quality over quantity',
            'Match formality level to the context (academic vs. casual)'
          ],
          examples: [
            Example(
              id: 'discourse_ex1',
              sentence: 'The project was challenging. Nevertheless, the team completed it on time.',
              explanation: '"Nevertheless" shows contrast - comma after it',
              type: ExampleType.correct,
              highlights: ['Nevertheless,'],
            ),
            Example(
              id: 'discourse_ex2',
              sentence: 'Not only did she finish early, but she also helped others.',
              explanation: '"Not only...but also" for emphasis and addition',
              type: ExampleType.correct,
              highlights: ['Not only', 'but also'],
            ),
          ],
          commonMistakes: [
            'However but → However, (don\'t use both)',
            'Although...but → Although... (choose one)',
            'Because of this reason → For this reason (avoid redundancy)'
          ],
          tips: [
            'Learn synonyms to avoid repetition: however = nevertheless = nonetheless',
            'Use formal markers in academic writing: furthermore, moreover, consequently',
            'Practice using them naturally - don\'t force them into every sentence'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createPunctuation() {
    return GrammarTopic(
      id: 'punctuation',
      title: 'Punctuation',
      description: 'Master essential punctuation marks to make your writing clear and professional.',
      icon: '❗',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing],
      estimatedTimeMinutes: 30,
      keyPoints: [
        'Full stops end sentences',
        'Commas separate ideas and lists',
        'Apostrophes show possession and contractions',
        'Question marks end questions',
      ],
      lessons: [
        Lesson(
          id: 'punctuation_basics',
          title: 'Essential Punctuation',
          estimatedTimeMinutes: 15,
          content: '''
**PUNCTUATION** makes your writing clear.

• **Full Stop (.)**: Ends a sentence
• **Comma (,)**: Separates ideas or lists
• **Apostrophe (')**: Shows possession (Tom's book) or contractions (it's = it is)
• **Question Mark (?)**: For questions

**Key Rules:**
Use punctuation correctly to avoid confusion and show professionalism.
          ''',
          type: LessonType.theory,
          rules: [
            'Every sentence ends with a full stop, question mark, or exclamation mark',
            'Use commas to separate items in a list',
            'Use apostrophes for possession (not plurals)',
            'Use commas before coordinating conjunctions in compound sentences'
          ],
          examples: [
            Example(
              id: 'punct_ex1',
              sentence: 'Tom\'s book is on the table.',
              explanation: 'Apostrophe shows possession',
              type: ExampleType.correct,
              highlights: ['Tom\'s'],
            ),
          ],
          commonMistakes: [
            'Its a nice day → It\'s a nice day',
            'The students books → The students\' books'
          ],
          tips: [
            'Read your writing aloud to check punctuation',
            'Remember: it\'s = it is, its = belonging to it',
            'Use commas to make your meaning clear'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createCommonMistakes() {
    return GrammarTopic(
      id: 'common_mistakes',
      title: 'Common Grammar Mistakes',
      description: 'Avoid the most frequent grammar errors that can hurt your IELTS score.',
      icon: '⚠️',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 45,
      keyPoints: [
        'Its vs it\'s distinction',
        'There, their, they\'re differences',
        'Avoiding double negatives',
        'Common word confusions',
      ],
      lessons: [
        Lesson(
          id: 'common_errors',
          title: 'Frequent Grammar Errors',
          estimatedTimeMinutes: 25,
          content: '''
**COMMON GRAMMAR MISTAKES** to avoid:

1. **Its vs It's**
   • Its = possession (The dog wagged its tail)
   • It's = it is (It's a beautiful day)

2. **There, Their, They're**
   • There = place (The book is there)
   • Their = possession (Their house is big)
   • They're = they are (They're coming soon)

3. **Double Negatives**
   • Wrong: I don't know nothing
   • Right: I don't know anything

4. **Subject-Verb Agreement**
   • Wrong: The students is here
   • Right: The students are here
          ''',
          type: LessonType.practice,
          rules: [
            'Check contractions: if you can say "it is", use "it\'s"',
            'Remember: their = belonging to them',
            'Avoid double negatives in formal English',
            'Make sure subjects and verbs agree in number'
          ],
          examples: [
            Example(
              id: 'mistake_ex1',
              sentence: 'The cat licked its paws.',
              explanation: '"Its" shows possession (no apostrophe)',
              type: ExampleType.correct,
              highlights: ['its'],
            ),
          ],
          commonMistakes: [
            'I don\'t have no money → I don\'t have any money',
            'There going to the store → They\'re going to the store'
          ],
          tips: [
            'When in doubt, expand contractions to check',
            'Proofread your writing for these common errors',
            'Practice these distinctions until they become automatic'
          ],
        ),
      ],
    );
  }

  static GrammarTopic _createSubjectVerbAgreement() {
    return GrammarTopic(
      id: 'subject_verb_agreement',
      title: 'Subject-Verb Agreement',
      description: 'Ensure subjects and verbs agree in number and person',
      icon: '⚖️',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 50,
      keyPoints: [
        'Basic agreement rules',
        'Agreement with compound subjects',
        'Agreement with collective nouns',
        'Agreement in complex sentences',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createRelativeClauses() {
    return GrammarTopic(
      id: 'relative_clauses',
      title: 'Relative Clauses',
      description: 'Connect ideas with defining and non-defining relative clauses',
      icon: '🔗',
      difficulty: DifficultyLevel.advanced,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.reading],
      estimatedTimeMinutes: 80,
      keyPoints: [
        'Defining vs non-defining clauses',
        'Relative pronouns (who, which, that, whose)',
        'Reduced relative clauses',
        'Relative clauses in formal writing',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createReportedSpeech() {
    return GrammarTopic(
      id: 'reported_speech',
      title: 'Reported Speech',
      description: 'Report what others have said using indirect speech',
      icon: '💬',
      difficulty: DifficultyLevel.intermediate,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 65,
      keyPoints: [
        'Tense changes in reported speech',
        'Reporting verbs',
        'Questions in reported speech',
        'Commands and requests',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createComparatives() {
    return GrammarTopic(
      id: 'comparatives_superlatives',
      title: 'Comparatives and Superlatives',
      description: 'Compare and contrast using comparative and superlative forms',
      icon: '📊',
      difficulty: DifficultyLevel.beginner,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 55,
      keyPoints: [
        'Comparative forms (-er, more)',
        'Superlative forms (-est, most)',
        'Irregular comparatives',
        'Comparative structures',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createGerunds() {
    return GrammarTopic(
      id: 'gerunds_infinitives',
      title: 'Gerunds and Infinitives',
      description: 'Know when to use -ing forms and to-infinitives',
      icon: '🔄',
      difficulty: DifficultyLevel.advanced,
      relevantSkills: [IELTSSkill.writing, IELTSSkill.speaking],
      estimatedTimeMinutes: 75,
      keyPoints: [
        'Verbs followed by gerunds',
        'Verbs followed by infinitives',
        'Verbs with different meanings',
        'Gerunds as subjects and objects',
      ],
      lessons: [],
    );
  }

  static GrammarTopic _createInversion() {
    return GrammarTopic(
      id: 'inversion',
      title: 'Inversion',
      description: 'Use inversion for emphasis and formal writing',
      icon: '🔀',
      difficulty: DifficultyLevel.advanced,
      relevantSkills: [IELTSSkill.writing],
      estimatedTimeMinutes: 45,
      keyPoints: [
        'Negative inversion',
        'Conditional inversion',
        'Inversion after adverbs',
        'Formal writing applications',
      ],
      lessons: [],
    );
  }
}

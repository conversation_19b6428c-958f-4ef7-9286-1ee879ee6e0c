class UserProfile {
  final String name;
  final String email;
  final String bio;
  final String gender;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserProfile({
    required this.name,
    required this.email,
    required this.bio,
    required this.gender,
    this.createdAt,
    this.updatedAt,
  });

  // Convert UserProfile to Map for storage
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'bio': bio,
      'gender': gender,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Create UserProfile from Map
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      bio: map['bio'] ?? '',
      gender: map['gender'] ?? 'Not specified',
      createdAt: map['createdAt'] != null 
          ? DateTime.parse(map['createdAt']) 
          : null,
      updatedAt: map['updatedAt'] != null 
          ? DateTime.parse(map['updatedAt']) 
          : null,
    );
  }

  // Create a copy of UserProfile with updated fields
  UserProfile copyWith({
    String? name,
    String? email,
    String? bio,
    String? gender,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      name: name ?? this.name,
      email: email ?? this.email,
      bio: bio ?? this.bio,
      gender: gender ?? this.gender,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Check if profile is complete
  bool get isComplete {
    return name.isNotEmpty && 
           email.isNotEmpty && 
           email.contains('@') &&
           gender != 'Not specified';
  }

  // Get display name (first name only)
  String get displayName {
    if (name.isEmpty) return 'User';
    return name.split(' ').first;
  }

  @override
  String toString() {
    return 'UserProfile(name: $name, email: $email, gender: $gender)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile &&
        other.name == name &&
        other.email == email &&
        other.bio == bio &&
        other.gender == gender;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        email.hashCode ^
        bio.hashCode ^
        gender.hashCode;
  }
}

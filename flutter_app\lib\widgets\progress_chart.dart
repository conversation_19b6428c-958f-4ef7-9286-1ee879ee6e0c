import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class Progress<PERSON><PERSON> extends StatelessWidget {
  final Map<String, double> skillScores;
  final double overallProgress;

  const ProgressChart({
    super.key,
    required this.skillScores,
    required this.overallProgress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Skill Progress',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Radar<PERSON>hart(
                RadarChartData(
                  radarTouchData: RadarTouchData(enabled: false),
                  dataSets: [
                    RadarDataSet(
                      fillColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                      borderColor: Theme.of(context).colorScheme.primary,
                      entryRadius: 3,
                      dataEntries: _getRadarEntries(),
                      borderWidth: 2,
                    ),
                  ],
                  radarBackgroundColor: Colors.transparent,
                  borderData: FlBorderData(show: false),
                  radarBorderData: BorderSide(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                  titlePositionPercentageOffset: 0.2,
                  titleTextStyle: Theme.of(context).textTheme.bodySmall!,
                  getTitle: (index, angle) {
                    final skills = skillScores.keys.toList();
                    if (index < skills.length) {
                      return RadarChartTitle(
                        text: skills[index].toUpperCase(),
                        angle: angle,
                      );
                    }
                    return const RadarChartTitle(text: '');
                  },
                  tickCount: 5,
                  ticksTextStyle: Theme.of(context).textTheme.bodySmall!.copyWith(
                    color: Colors.grey[600],
                    fontSize: 10,
                  ),
                  tickBorderData: BorderSide(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                  gridBorderData: BorderSide(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildOverallProgress(context),
          ],
        ),
      ),
    );
  }

  List<RadarEntry> _getRadarEntries() {
    return skillScores.values.map((score) => RadarEntry(value: score)).toList();
  }

  Widget _buildOverallProgress(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Overall Progress',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${overallProgress.toInt()}%',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: overallProgress / 100,
          backgroundColor: Colors.grey[200],
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }
}

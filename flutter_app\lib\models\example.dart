enum ExampleType { correct, incorrect, comparison }

class Example {
  final String id;
  final String sentence;
  final String explanation;
  final ExampleType type;
  final String? correction;
  final List<String> highlights;
  final String? context;

  const Example({
    required this.id,
    required this.sentence,
    required this.explanation,
    required this.type,
    this.correction,
    required this.highlights,
    this.context,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sentence': sentence,
      'explanation': explanation,
      'type': type.name,
      'correction': correction,
      'highlights': highlights,
      'context': context,
    };
  }

  factory Example.fromMap(Map<String, dynamic> map) {
    return Example(
      id: map['id'] ?? '',
      sentence: map['sentence'] ?? '',
      explanation: map['explanation'] ?? '',
      type: ExampleType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => ExampleType.correct,
      ),
      correction: map['correction'],
      highlights: List<String>.from(map['highlights'] ?? []),
      context: map['context'],
    );
  }

  Example copyWith({
    String? id,
    String? sentence,
    String? explanation,
    ExampleType? type,
    String? correction,
    List<String>? highlights,
    String? context,
  }) {
    return Example(
      id: id ?? this.id,
      sentence: sentence ?? this.sentence,
      explanation: explanation ?? this.explanation,
      type: type ?? this.type,
      correction: correction ?? this.correction,
      highlights: highlights ?? this.highlights,
      context: context ?? this.context,
    );
  }

  String get typeText {
    switch (type) {
      case ExampleType.correct:
        return 'Correct Usage';
      case ExampleType.incorrect:
        return 'Common Mistake';
      case ExampleType.comparison:
        return 'Comparison';
    }
  }

  bool get hasCorrection => correction != null && correction!.isNotEmpty;
}

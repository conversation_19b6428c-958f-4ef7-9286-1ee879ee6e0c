import 'lesson.dart';

enum DifficultyLevel { beginner, intermediate, advanced }

enum IELTSSkill { writing, reading, listening, speaking }

class GrammarTopic {
  final String id;
  final String title;
  final String description;
  final String icon;
  final DifficultyLevel difficulty;
  final List<IELTSSkill> relevantSkills;
  final List<Lesson> lessons;
  final int estimatedTimeMinutes;
  final List<String> keyPoints;
  final bool isCompleted;
  final double progressPercentage;
  final int totalQuizzesTaken;
  final double averageScore;

  const GrammarTopic({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.difficulty,
    required this.relevantSkills,
    required this.lessons,
    required this.estimatedTimeMinutes,
    required this.keyPoints,
    this.isCompleted = false,
    this.progressPercentage = 0.0,
    this.totalQuizzesTaken = 0,
    this.averageScore = 0.0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'difficulty': difficulty.name,
      'relevantSkills': relevantSkills.map((skill) => skill.name).toList(),
      'lessons': lessons.map((lesson) => lesson.toMap()).toList(),
      'estimatedTimeMinutes': estimatedTimeMinutes,
      'keyPoints': keyPoints,
      'isCompleted': isCompleted,
      'progressPercentage': progressPercentage,
      'totalQuizzesTaken': totalQuizzesTaken,
      'averageScore': averageScore,
    };
  }

  factory GrammarTopic.fromMap(Map<String, dynamic> map) {
    return GrammarTopic(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      icon: map['icon'] ?? '',
      difficulty: DifficultyLevel.values.firstWhere(
        (level) => level.name == map['difficulty'],
        orElse: () => DifficultyLevel.beginner,
      ),
      relevantSkills: (map['relevantSkills'] as List<dynamic>?)
              ?.map((skill) => IELTSSkill.values.firstWhere(
                    (s) => s.name == skill,
                    orElse: () => IELTSSkill.writing,
                  ))
              .toList() ??
          [],
      lessons: (map['lessons'] as List<dynamic>?)
              ?.map((lesson) => Lesson.fromMap(lesson))
              .toList() ??
          [],
      estimatedTimeMinutes: map['estimatedTimeMinutes'] ?? 0,
      keyPoints: List<String>.from(map['keyPoints'] ?? []),
      isCompleted: map['isCompleted'] ?? false,
      progressPercentage: map['progressPercentage']?.toDouble() ?? 0.0,
      totalQuizzesTaken: map['totalQuizzesTaken'] ?? 0,
      averageScore: map['averageScore']?.toDouble() ?? 0.0,
    );
  }

  GrammarTopic copyWith({
    String? id,
    String? title,
    String? description,
    String? icon,
    DifficultyLevel? difficulty,
    List<IELTSSkill>? relevantSkills,
    List<Lesson>? lessons,
    int? estimatedTimeMinutes,
    List<String>? keyPoints,
    bool? isCompleted,
    double? progressPercentage,
    int? totalQuizzesTaken,
    double? averageScore,
  }) {
    return GrammarTopic(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      difficulty: difficulty ?? this.difficulty,
      relevantSkills: relevantSkills ?? this.relevantSkills,
      lessons: lessons ?? this.lessons,
      estimatedTimeMinutes: estimatedTimeMinutes ?? this.estimatedTimeMinutes,
      keyPoints: keyPoints ?? this.keyPoints,
      isCompleted: isCompleted ?? this.isCompleted,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      totalQuizzesTaken: totalQuizzesTaken ?? this.totalQuizzesTaken,
      averageScore: averageScore ?? this.averageScore,
    );
  }

  String get difficultyText {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return 'Beginner';
      case DifficultyLevel.intermediate:
        return 'Intermediate';
      case DifficultyLevel.advanced:
        return 'Advanced';
    }
  }

  String get skillsText {
    return relevantSkills.map((skill) => skill.name.toUpperCase()).join(', ');
  }

  int get completedLessons {
    return lessons.where((lesson) => lesson.isCompleted).length;
  }

  int get totalLessons {
    return lessons.length;
  }
}

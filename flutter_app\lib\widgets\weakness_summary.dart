import 'package:flutter/material.dart';
import '../models/weakness_analysis.dart';

class WeaknessSummary extends StatelessWidget {
  final WeaknessAnalysis weaknessAnalysis;
  final VoidCallback? onViewDetails;

  const WeaknessSummary({
    super.key,
    required this.weaknessAnalysis,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final criticalWeaknesses = weaknessAnalysis.criticalWeaknesses;
    final majorWeaknesses = weaknessAnalysis.majorWeaknesses;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_amber,
                  color: Colors.orange[700],
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Weakness Analysis',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onViewDetails != null)
                  TextButton(
                    onPressed: onViewDetails,
                    child: const Text('View Details'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (criticalWeaknesses.isEmpty && majorWeaknesses.isEmpty)
              _buildNoWeaknesses(context)
            else
              _buildWeaknessList(context, criticalWeaknesses, majorWeaknesses),
            const SizedBox(height: 16),
            _buildPriorityTopics(context),
          ],
        ),
      ),
    );
  }

  Widget _buildNoWeaknesses(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green[700],
            size: 32,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Great Progress!',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
                Text(
                  'No critical weaknesses detected. Keep up the excellent work!',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeaknessList(
    BuildContext context,
    List<GrammarWeakness> criticalWeaknesses,
    List<GrammarWeakness> majorWeaknesses,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (criticalWeaknesses.isNotEmpty) ...[
          Text(
            'Critical Areas (${criticalWeaknesses.length})',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.red[700],
            ),
          ),
          const SizedBox(height: 8),
          ...criticalWeaknesses.take(2).map((weakness) => 
            _buildWeaknessItem(context, weakness, Colors.red)),
          const SizedBox(height: 12),
        ],
        if (majorWeaknesses.isNotEmpty) ...[
          Text(
            'Major Areas (${majorWeaknesses.length})',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 8),
          ...majorWeaknesses.take(2).map((weakness) => 
            _buildWeaknessItem(context, weakness, Colors.orange)),
        ],
      ],
    );
  }

  Widget _buildWeaknessItem(
    BuildContext context,
    GrammarWeakness weakness,
    MaterialColor color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color[200]!),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color[600],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  weakness.category,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color[700],
                  ),
                ),
                Text(
                  weakness.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                weakness.accuracyText,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color[700],
                ),
              ),
              Text(
                '${weakness.frequency}x',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityTopics(BuildContext context) {
    if (weaknessAnalysis.priorityTopics.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Priority Topics',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: weaknessAnalysis.priorityTopics.take(3).map((topic) =>
            Chip(
              label: Text(
                topic,
                style: const TextStyle(fontSize: 12),
              ),
              backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              side: BorderSide(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
            ),
          ).toList(),
        ),
      ],
    );
  }
}

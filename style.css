
:root {
    --primary-color: #D97706;
    --dark-color: #1F2937;
    --text-color: #4B5563;
    --light-gray: #F9FAFB;
    --white: #FFFFFF;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3 {
    color: var(--dark-color);
    font-weight: 600;
}

h1 {
    font-size: 3.5rem;
    line-height: 1.2;
}

h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 20px;
}

.section-subtitle {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 40px;
}

.btn {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 12px 28px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    text-transform: uppercase;
}

header {
    background-color: var(--white);
    padding: 20px 0;
    border-bottom: 1px solid #E5E7EB;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 30px;
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
}

.cart {
    text-decoration: none;
    color: var(--dark-color);
    font-weight: 500;
}




.hero-section {
    padding: 60px 0;
    background-color: var(--light-gray);
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 40px;
}

.hero-text {
    flex: 1;
}

.hero-text p {
    margin: 20px 0;
}

.hero-image {
    flex: 1;
}

.hero-image img {
    max-width: 100%;
    border-radius: 8px;
}



.plants-section {
    padding: 80px 0;
}

.plants-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.plant-card {
    text-align: center;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    padding: 20px;
    background-color: var(--white);
}

.plant-card img {
    max-width: 100%;
    margin-bottom: 15px;
}

.plant-card h3 {
    font-size: 1.25rem;
}

.plant-card .price {
    color: var(--dark-color);
    font-weight: 600;
    margin: 10px 0 20px;
}





.lover-section {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.lover-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.lover-image {
    flex: 1;
    position: relative;
}

.lover-image > img {
    max-width: 100%;
    border-radius: 8px;
}

.trusted-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 100px;
}

.lover-text {
    flex: 1;
}

.lover-text h2 {
    text-align: left;
}

.lover-text ul {
    list-style-type: '• ';
    padding-left: 20px;
}

.lover-text li {
    margin-bottom: 15px;
}





.deals-section {
    padding: 80px 0;
}

.deals-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    grid-template-areas:
        "small1 large"
        "small2 large";
}

.deal-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
    background-size: cover;
    background-position: center;
}

.deal-card::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.deal-content {
    position: relative;
    z-index: 2;
}
.deal-content h3 {
    color: var(--white);
    font-size: 1.8rem;
}
.shop-now {
    color: var(--white);
    text-decoration: underline;
    font-weight: 500;
    margin-top: 10px;
    display: inline-block;
}

.deal-card.small {
    min-height: 250px;
}
.deal-card.small:nth-of-type(1) { grid-area: small1; }
.deal-card.small:nth-of-type(2) { grid-area: small2; }
.deal-card.large {
    grid-area: large;
    min-height: 520px;
}





.newsletter-section {
    padding: 80px 0;
    background-size: cover;
    background-position: center;
    color: var(--white);
}

.newsletter-content h2 {
    color: var(--white);
}

.newsletter-form {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.newsletter-form input {
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #ccc;
    width: 300px;
    font-family: 'Poppins', sans-serif;
}




footer {
    background-color: var(--dark-color);
    color: #D1D5DB;
    padding: 60px 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    gap: 40px;
}

.footer-about {
    flex-basis: 40%;
}

.footer-logo {
    height: 30px;
    margin-bottom: 20px;
    filter: brightness(0) invert(1);
}

.social-icons {
    margin-top: 20px;
    display: flex;
    gap: 15px;
}

.social-icons img {
    height: 24px;
}

.footer-links {
    display: flex;
    gap: 60px;
}

.footer-column h4 {
    color: var(--white);
    margin-bottom: 15px;
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 10px;
}

.footer-column a {
    color: #D1D5DB;
    text-decoration: none;
}




@media (max-width: 992px) {
    h1 { font-size: 2.8rem; }
    h2 { font-size: 2rem; }

    .plants-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .lover-content {
        flex-direction: column;
    }
    .lover-text h2 {
        text-align: center;
        margin-top: 20px;
    }

    .deals-grid {
        grid-template-columns: 1fr;
         grid-template-areas:
            "large"
            "small1"
            "small2";
    }
     .deal-card.large {
        min-height: 400px;
    }
}


@media (max-width: 768px) {
    .nav-links {
        display: none; 
    }

    .hero-content {
        flex-direction: column;
        text-align: center;
    }

    .hero-text {
        order: 2;
    }
    .hero-image {
        order: 1;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-links {
       flex-direction: column;
       gap: 30px;
       text-align: center;
    }
}

@media (max-width: 576px) {
    h1 { font-size: 2.2rem; }

    .plants-grid {
        grid-template-columns: 1fr;
    }
    .newsletter-form {
        flex-direction: column;
        align-items: center;
    }
    .newsletter-form input {
        width: 100%;
        max-width: 300px;
    }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/learning_provider.dart';
import '../models/grammar_topic.dart';
import '../widgets/topic_card.dart';
import '../widgets/progress_chart.dart';
import '../widgets/weakness_summary.dart';
import '../widgets/recommendation_card.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  List<String> _recommendations = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  Future<void> _initializeData() async {
    final provider = Provider.of<LearningProvider>(context, listen: false);
    await provider.initialize();
    _loadRecommendations();
  }

  Future<void> _loadRecommendations() async {
    final provider = Provider.of<LearningProvider>(context, listen: false);
    final recommendations = await provider.getPersonalizedRecommendations();
    setState(() {
      _recommendations = recommendations;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          _buildHomeTab(),
          _buildTopicsTab(),
          _buildProgressTab(),
          _buildWeaknessTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book),
            label: 'Topics',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Progress',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.warning),
            label: 'Weaknesses',
          ),
        ],
      ),
    );
  }

  Widget _buildHomeTab() {
    return Consumer<LearningProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[300],
                ),
                const SizedBox(height: 16),
                Text(
                  'Something went wrong',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  provider.error!,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _initializeData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                title: const Text('IELTS Grammar Master'),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context).colorScheme.primary.withOpacity(0.8),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWelcomeCard(provider),
                    const SizedBox(height: 16),
                    _buildQuickStats(provider),
                    const SizedBox(height: 24),
                    _buildRecommendedTopics(provider),
                    const SizedBox(height: 24),
                    _buildRecommendations(),
                    const SizedBox(height: 24),
                    _buildRecentActivity(provider),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildWelcomeCard(LearningProvider provider) {
    final userProgress = provider.userProgress;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.waving_hand,
                  color: Colors.orange,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Welcome back!',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (userProgress != null) ...[
              Text(
                'Study streak: ${userProgress.studyStreakDays} days 🔥',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Overall accuracy: ${userProgress.accuracyPercentage.toInt()}%',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Total study time: ${userProgress.studyTimeText}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(LearningProvider provider) {
    final userProgress = provider.userProgress;
    
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Topics',
            '${provider.topics.length}',
            Icons.book,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Completed',
            '${userProgress?.completedTopics ?? 0}',
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Quizzes',
            '${userProgress?.totalQuizzesTaken ?? 0}',
            Icons.quiz,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendedTopics(LearningProvider provider) {
    final recommendedTopics = provider.getRecommendedTopics();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recommended for You',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: recommendedTopics.length,
            itemBuilder: (context, index) {
              final topic = recommendedTopics[index];
              return SizedBox(
                width: 280,
                child: TopicCard(
                  topic: topic,
                  completionPercentage: provider.getTopicCompletionPercentage(topic.id),
                  onTap: () => _navigateToTopic(topic),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendations() {
    if (_recommendations.isEmpty) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'AI Recommendations',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...(_recommendations.take(3).map((recommendation) => 
          RecommendationCard(recommendation: recommendation)
        )),
      ],
    );
  }

  Widget _buildRecentActivity(LearningProvider provider) {
    final recentQuizzes = provider.recentQuizzes.take(3).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (recentQuizzes.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(
                    Icons.quiz_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No recent activity',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start a quiz to see your activity here',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          ...recentQuizzes.map((quiz) => Card(
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: quiz.scorePercentage >= 70 
                    ? Colors.green[100] 
                    : Colors.orange[100],
                child: Icon(
                  quiz.scorePercentage >= 70 
                      ? Icons.check 
                      : Icons.warning,
                  color: quiz.scorePercentage >= 70 
                      ? Colors.green[700] 
                      : Colors.orange[700],
                ),
              ),
              title: Text(quiz.title),
              subtitle: Text(
                'Score: ${quiz.scorePercentage.toInt()}% • ${quiz.typeText}',
              ),
              trailing: Text(
                _formatDate(quiz.completedAt!),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          )),
      ],
    );
  }

  Widget _buildTopicsTab() {
    return Consumer<LearningProvider>(
      builder: (context, provider, child) {
        return CustomScrollView(
          slivers: [
            const SliverAppBar(
              title: Text('Grammar Topics'),
              floating: true,
            ),
            SliverPadding(
              padding: const EdgeInsets.all(16),
              sliver: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.65,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final topic = provider.topics[index];
                    return TopicCard(
                      topic: topic,
                      completionPercentage: provider.getTopicCompletionPercentage(topic.id),
                      onTap: () => _navigateToTopic(topic),
                    );
                  },
                  childCount: provider.topics.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildProgressTab() {
    return const Center(
      child: Text('Progress Tab - Coming Soon'),
    );
  }

  Widget _buildWeaknessTab() {
    return const Center(
      child: Text('Weakness Tab - Coming Soon'),
    );
  }

  void _navigateToTopic(GrammarTopic topic) {
    Navigator.pushNamed(
      context,
      '/topic',
      arguments: topic.id,
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }
}

import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/quiz.dart';
import '../models/quiz_question.dart';
import '../models/weakness_analysis.dart';
import '../models/user_progress.dart';

class AIService {
  static const String _apiKey = 'AIzaSyC2wEDAqRtrwlwGalkmH0Uz2rl117fRFf8';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  Future<List<QuizQuestion>> generateQuiz({
    required String topicId,
    required String topicTitle,
    required List<String> grammarRules,
    required QuizDifficulty difficulty,
    required int questionCount,
    List<String>? focusWeaknesses,
  }) async {
    try {
      final prompt = _buildQuizPrompt(
        topicTitle: topicTitle,
        grammarRules: grammarRules,
        difficulty: difficulty,
        questionCount: questionCount,
        focusWeaknesses: focusWeaknesses,
      );

      final response = await _makeAPICall(prompt);
      return _parseQuizResponse(response, topicId);
    } catch (e) {
      throw Exception('Failed to generate quiz: $e');
    }
  }

  Future<WeaknessAnalysis> analyzeUserWeaknesses({
    required UserProgress userProgress,
    required List<Quiz> recentQuizzes,
  }) async {
    try {
      final prompt = _buildWeaknessAnalysisPrompt(userProgress, recentQuizzes);
      final response = await _makeAPICall(prompt);
      return _parseWeaknessAnalysis(response);
    } catch (e) {
      throw Exception('Failed to analyze weaknesses: $e');
    }
  }

  Future<List<String>> generatePersonalizedRecommendations({
    required WeaknessAnalysis weaknessAnalysis,
    required UserProgress userProgress,
  }) async {
    try {
      final prompt = _buildRecommendationPrompt(weaknessAnalysis, userProgress);
      final response = await _makeAPICall(prompt);
      return _parseRecommendations(response);
    } catch (e) {
      throw Exception('Failed to generate recommendations: $e');
    }
  }

  Future<String> _makeAPICall(String prompt) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'X-goog-api-key': _apiKey,
      },
      body: jsonEncode({
        'contents': [
          {
            'parts': [
              {'text': prompt}
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 2048,
        }
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['candidates'][0]['content']['parts'][0]['text'];
    } else {
      throw Exception('API call failed: ${response.statusCode} - ${response.body}');
    }
  }

  String _buildQuizPrompt({
    required String topicTitle,
    required List<String> grammarRules,
    required QuizDifficulty difficulty,
    required int questionCount,
    List<String>? focusWeaknesses,
  }) {
    final weaknessText = focusWeaknesses?.isNotEmpty == true
        ? '\nFocus on these specific weaknesses: ${focusWeaknesses!.join(", ")}'
        : '';

    return '''
Generate $questionCount IELTS grammar questions for the topic: $topicTitle

Grammar Rules to Cover:
${grammarRules.map((rule) => '- $rule').join('\n')}

Difficulty Level: ${difficulty.name}
$weaknessText

Requirements:
1. Create diverse question types: multiple choice, fill-in-the-blank, error correction, sentence completion
2. Each question should test specific grammar rules
3. Provide clear explanations for correct answers
4. Include realistic IELTS-style contexts
5. Ensure questions are appropriate for ${difficulty.name} level

Format your response as JSON:
{
  "questions": [
    {
      "id": "unique_id",
      "question": "Question text",
      "type": "multipleChoice|fillInTheBlank|errorCorrection|sentenceCompletion",
      "options": ["option1", "option2", "option3", "option4"], // for multiple choice only
      "correctAnswer": "correct answer",
      "acceptableAnswers": ["alternative1", "alternative2"], // optional alternatives
      "explanation": "Why this is correct",
      "grammarRule": "Specific grammar rule being tested",
      "category": "Grammar category (e.g., Present Perfect)",
      "points": 1
    }
  ]
}
''';
  }

  String _buildWeaknessAnalysisPrompt(UserProgress userProgress, List<Quiz> recentQuizzes) {
    final quizData = recentQuizzes.map((quiz) => {
      'topicId': quiz.topicId,
      'score': quiz.scorePercentage,
      'weaknesses': quiz.weaknessesIdentified,
      'categoryScores': quiz.categoryScores,
    }).toList();

    return '''
Analyze the user's grammar weaknesses based on their performance data:

Overall Stats:
- Total Questions Answered: ${userProgress.totalQuestionsAnswered}
- Overall Accuracy: ${userProgress.accuracyPercentage.toInt()}%
- Study Streak: ${userProgress.studyStreakDays} days

Recent Quiz Performance:
${jsonEncode(quizData)}

Current Skill Scores:
${jsonEncode(userProgress.skillScores)}

Please provide a comprehensive weakness analysis in JSON format:
{
  "weaknesses": {
    "category_name": {
      "category": "Grammar category",
      "description": "Detailed description of the weakness",
      "level": "minor|moderate|major|critical",
      "frequency": number_of_occurrences,
      "accuracyRate": decimal_accuracy_rate,
      "commonMistakes": ["mistake1", "mistake2"],
      "recommendedLessons": ["lesson1", "lesson2"],
      "isImproving": boolean
    }
  },
  "priorityTopics": ["topic1", "topic2", "topic3"],
  "categoryScores": {
    "category": score
  }
}

Focus on identifying patterns, recurring mistakes, and areas needing immediate attention for IELTS success.
''';
  }

  String _buildRecommendationPrompt(WeaknessAnalysis weaknessAnalysis, UserProgress userProgress) {
    return '''
Based on the user's weakness analysis and progress, generate personalized study recommendations:

Current Weaknesses:
${jsonEncode(weaknessAnalysis.weaknesses.map((key, value) => MapEntry(key, {
      'level': value.levelText,
      'accuracy': value.accuracyText,
      'frequency': value.frequency,
    })))}

Priority Topics: ${weaknessAnalysis.priorityTopics.join(', ')}
Overall Progress: ${userProgress.overallProgress.toInt()}%
Study Streak: ${userProgress.studyStreakDays} days

Generate 5-8 specific, actionable recommendations as a JSON array:
[
  "Recommendation 1: Focus on [specific area] by practicing [specific activity]",
  "Recommendation 2: Review [topic] lessons, especially [specific concept]",
  "Recommendation 3: Take targeted quizzes on [weakness area]",
  ...
]

Make recommendations specific, actionable, and prioritized by importance for IELTS band 9 achievement.
''';
  }

  List<QuizQuestion> _parseQuizResponse(String response, String topicId) {
    try {
      // Extract JSON from response (handle potential markdown formatting)
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}') + 1;
      final jsonString = response.substring(jsonStart, jsonEnd);
      
      final data = jsonDecode(jsonString);
      final questions = data['questions'] as List;

      return questions.map((q) => QuizQuestion(
        id: q['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
        question: q['question'] ?? '',
        type: _parseQuestionType(q['type']),
        options: List<String>.from(q['options'] ?? []),
        correctAnswer: q['correctAnswer'] ?? '',
        acceptableAnswers: List<String>.from(q['acceptableAnswers'] ?? []),
        explanation: q['explanation'] ?? '',
        grammarRule: q['grammarRule'] ?? '',
        points: q['points'] ?? 1,
        category: q['category'] ?? topicId,
      )).toList();
    } catch (e) {
      // Fallback: create sample questions if parsing fails
      return _createFallbackQuestions(topicId);
    }
  }

  WeaknessAnalysis _parseWeaknessAnalysis(String response) {
    try {
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}') + 1;
      final jsonString = response.substring(jsonStart, jsonEnd);
      
      final data = jsonDecode(jsonString);
      
      final weaknesses = <String, GrammarWeakness>{};
      if (data['weaknesses'] != null) {
        (data['weaknesses'] as Map<String, dynamic>).forEach((key, value) {
          weaknesses[key] = GrammarWeakness(
            category: value['category'] ?? key,
            description: value['description'] ?? '',
            level: _parseWeaknessLevel(value['level']),
            frequency: value['frequency'] ?? 0,
            accuracyRate: (value['accuracyRate'] ?? 0.0).toDouble(),
            commonMistakes: List<String>.from(value['commonMistakes'] ?? []),
            recommendedLessons: List<String>.from(value['recommendedLessons'] ?? []),
            firstIdentified: DateTime.now().subtract(const Duration(days: 7)),
            lastOccurrence: DateTime.now(),
            isImproving: value['isImproving'] ?? false,
          );
        });
      }

      return WeaknessAnalysis(
        weaknesses: weaknesses,
        priorityTopics: List<String>.from(data['priorityTopics'] ?? []),
        categoryScores: Map<String, double>.from(data['categoryScores'] ?? {}),
        lastAnalyzed: DateTime.now(),
        totalAnalyses: 1,
      );
    } catch (e) {
      return WeaknessAnalysis(
        lastAnalyzed: DateTime.now(),
        totalAnalyses: 1,
      );
    }
  }

  List<String> _parseRecommendations(String response) {
    try {
      final jsonStart = response.indexOf('[');
      final jsonEnd = response.lastIndexOf(']') + 1;
      final jsonString = response.substring(jsonStart, jsonEnd);
      
      final data = jsonDecode(jsonString) as List;
      return data.cast<String>();
    } catch (e) {
      return [
        'Focus on your weakest grammar areas identified in the analysis',
        'Practice with targeted quizzes daily for consistent improvement',
        'Review grammar rules before attempting practice questions',
        'Take regular assessments to track your progress',
        'Study for at least 30 minutes daily to maintain momentum',
      ];
    }
  }

  IELTSQuestionType _parseQuestionType(String? type) {
    switch (type?.toLowerCase()) {
      case 'multiplechoice':
        return IELTSQuestionType.multipleChoice;
      case 'fillintheblank':
        return IELTSQuestionType.fillInTheBlank;
      case 'errorcorrection':
        return IELTSQuestionType.errorCorrection;
      case 'sentencecompletion':
        return IELTSQuestionType.sentenceCompletion;
      default:
        return IELTSQuestionType.multipleChoice;
    }
  }

  WeaknessLevel _parseWeaknessLevel(String? level) {
    switch (level?.toLowerCase()) {
      case 'critical':
        return WeaknessLevel.critical;
      case 'major':
        return WeaknessLevel.major;
      case 'moderate':
        return WeaknessLevel.moderate;
      default:
        return WeaknessLevel.minor;
    }
  }

  List<QuizQuestion> _createFallbackQuestions(String topicId) {
    return [
      QuizQuestion(
        id: '1',
        question: 'Choose the correct form: "I _____ to London three times this year."',
        type: IELTSQuestionType.multipleChoice,
        options: ['have been', 'was', 'am going', 'will go'],
        correctAnswer: 'have been',
        explanation: 'Present Perfect is used for experiences up to now.',
        grammarRule: 'Present Perfect for experiences',
        category: topicId,
      ),
    ];
  }
}

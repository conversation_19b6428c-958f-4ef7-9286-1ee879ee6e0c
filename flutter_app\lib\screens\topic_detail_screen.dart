import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/learning_provider.dart';
import '../models/grammar_topic.dart';
import '../models/quiz.dart';

class TopicDetailScreen extends StatelessWidget {
  const TopicDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final topicId = ModalRoute.of(context)!.settings.arguments as String;
    
    return Consumer<LearningProvider>(
      builder: (context, provider, child) {
        final topic = provider.getTopicById(topicId);
        
        if (topic == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Topic Not Found')),
            body: const Center(
              child: Text('Topic not found'),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(topic.title),
            actions: [
              IconButton(
                icon: const Icon(Icons.quiz),
                onPressed: () => _startQuiz(context, provider, topic),
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTopicHeader(context, topic, provider),
                const SizedBox(height: 24),
                _buildKeyPoints(context, topic),
                const SizedBox(height: 24),
                _buildLessons(context, topic, provider),
                const SizedBox(height: 24),
                _buildQuickActions(context, provider, topic),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopicHeader(BuildContext context, GrammarTopic topic, LearningProvider provider) {
    final completionPercentage = provider.getTopicCompletionPercentage(topic.id);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      topic.icon,
                      style: const TextStyle(fontSize: 28),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        topic.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        topic.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildInfoChip(
                  context,
                  Icons.schedule,
                  '${topic.estimatedTimeMinutes} min',
                ),
                const SizedBox(width: 8),
                _buildInfoChip(
                  context,
                  Icons.signal_cellular_alt,
                  topic.difficultyText,
                ),
                const SizedBox(width: 8),
                _buildInfoChip(
                  context,
                  Icons.book,
                  '${topic.lessons.length} lessons',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'Progress: ${completionPercentage.toInt()}%',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  '${topic.lessons.where((l) => l.isCompleted).length}/${topic.lessons.length} completed',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: completionPercentage / 100,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context, IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyPoints(BuildContext context, GrammarTopic topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Key Learning Points',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: topic.keyPoints.map((point) => 
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        margin: const EdgeInsets.only(top: 8),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          point,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ),
              ).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLessons(BuildContext context, GrammarTopic topic, LearningProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Lessons',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...topic.lessons.map((lesson) => Card(
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: lesson.isCompleted 
                  ? Colors.green[100] 
                  : Theme.of(context).colorScheme.primary.withOpacity(0.1),
              child: Icon(
                lesson.isCompleted ? Icons.check : Icons.play_arrow,
                color: lesson.isCompleted 
                    ? Colors.green[700] 
                    : Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Text(
              lesson.title,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(lesson.typeText),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${lesson.estimatedTimeMinutes} min',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: lesson.isCompleted 
                ? Icon(Icons.check_circle, color: Colors.green[700])
                : const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _openLesson(context, topic.id, lesson.id),
          ),
        )),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, LearningProvider provider, GrammarTopic topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _startQuiz(context, provider, topic),
                icon: const Icon(Icons.quiz),
                label: const Text('Practice Quiz'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _startWeaknessQuiz(context, provider, topic),
                icon: const Icon(Icons.center_focus_strong),
                label: const Text('Focus Quiz'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _openLesson(BuildContext context, String topicId, String lessonId) {
    Navigator.pushNamed(
      context,
      '/lesson',
      arguments: {'topicId': topicId, 'lessonId': lessonId},
    );
  }

  void _startQuiz(BuildContext context, LearningProvider provider, GrammarTopic topic) async {
    try {
      final quiz = await provider.generateQuiz(
        topicId: topic.id,
        difficulty: QuizDifficulty.medium,
        questionCount: 10,
      );
      
      if (context.mounted) {
        Navigator.pushNamed(
          context,
          '/quiz',
          arguments: quiz,
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to generate quiz: $e')),
        );
      }
    }
  }

  void _startWeaknessQuiz(BuildContext context, LearningProvider provider, GrammarTopic topic) async {
    final weaknesses = provider.userProgress?.weaknessAnalysis.priorityTopics ?? [];
    
    try {
      final quiz = await provider.generateQuiz(
        topicId: topic.id,
        difficulty: QuizDifficulty.medium,
        questionCount: 8,
        focusWeaknesses: weaknesses,
      );
      
      if (context.mounted) {
        Navigator.pushNamed(
          context,
          '/quiz',
          arguments: quiz,
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to generate focus quiz: $e')),
        );
      }
    }
  }
}

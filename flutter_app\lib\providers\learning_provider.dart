import 'package:flutter/foundation.dart';
import '../models/grammar_topic.dart';
import '../models/lesson.dart';
import '../models/quiz.dart';
import '../models/user_progress.dart';
import '../models/weakness_analysis.dart';
import '../data/grammar_data.dart';
import '../services/ai_service.dart';

class LearningProvider extends ChangeNotifier {
  final AIService _aiService = AIService();
  
  List<GrammarTopic> _topics = [];
  UserProgress? _userProgress;
  List<Quiz> _recentQuizzes = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<GrammarTopic> get topics => _topics;
  UserProgress? get userProgress => _userProgress;
  List<Quiz> get recentQuizzes => _recentQuizzes;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize data
  Future<void> initialize() async {
    _setLoading(true);
    try {
      _topics = GrammarData.getAllTopics();
      await _loadUserProgress();
      _setError(null);
    } catch (e) {
      _setError('Failed to initialize: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load user progress
  Future<void> _loadUserProgress() async {
    // In a real app, this would load from local storage or API
    _userProgress = UserProgress(
      userId: 'user_001',
      weaknessAnalysis: WeaknessAnalysis(
        lastAnalyzed: DateTime.now().subtract(const Duration(days: 1)),
        totalAnalyses: 5,
      ),
      lastStudyDate: DateTime.now().subtract(const Duration(hours: 2)),
      studyStreakDays: 7,
      totalStudyTimeMinutes: 450,
      overallAccuracy: 0.78,
      totalQuizzesTaken: 12,
      totalQuestionsAnswered: 156,
      totalCorrectAnswers: 122,
      achievements: [
        'First Quiz Completed',
        'Week Streak',
        'Grammar Explorer',
      ],
      skillScores: {
        'writing': 7.5,
        'speaking': 7.0,
        'reading': 8.0,
        'listening': 7.5,
      },
    );
  }

  // Get topic by ID
  GrammarTopic? getTopicById(String id) {
    try {
      return _topics.firstWhere((topic) => topic.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get lesson by ID
  Lesson? getLessonById(String topicId, String lessonId) {
    final topic = getTopicById(topicId);
    if (topic == null) return null;
    
    try {
      return topic.lessons.firstWhere((lesson) => lesson.id == lessonId);
    } catch (e) {
      return null;
    }
  }

  // Generate quiz for topic
  Future<Quiz> generateQuiz({
    required String topicId,
    required QuizDifficulty difficulty,
    int questionCount = 10,
    List<String>? focusWeaknesses,
  }) async {
    _setLoading(true);
    try {
      final topic = getTopicById(topicId);
      if (topic == null) throw Exception('Topic not found');

      final questions = await _aiService.generateQuiz(
        topicId: topicId,
        topicTitle: topic.title,
        grammarRules: topic.keyPoints,
        difficulty: difficulty,
        questionCount: questionCount,
        focusWeaknesses: focusWeaknesses,
      );

      final quiz = Quiz(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '${topic.title} Quiz',
        topicId: topicId,
        type: focusWeaknesses?.isNotEmpty == true 
            ? QuizType.weakness_focus 
            : QuizType.practice,
        difficulty: difficulty,
        questions: questions,
        timeLimit: questionCount * 2, // 2 minutes per question
        createdAt: DateTime.now(),
        totalQuestions: questions.length,
      );

      _setError(null);
      return quiz;
    } catch (e) {
      _setError('Failed to generate quiz: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Complete quiz
  Future<void> completeQuiz(Quiz quiz) async {
    _setLoading(true);
    try {
      final completedQuiz = quiz.copyWith(
        isCompleted: true,
        completedAt: DateTime.now(),
        score: quiz.questions.where((q) => q.isCorrect == true).length,
      );

      _recentQuizzes.insert(0, completedQuiz);
      if (_recentQuizzes.length > 10) {
        _recentQuizzes = _recentQuizzes.take(10).toList();
      }

      // Update user progress
      if (_userProgress != null) {
        _userProgress = _userProgress!.copyWith(
          totalQuizzesTaken: _userProgress!.totalQuizzesTaken + 1,
          totalQuestionsAnswered: _userProgress!.totalQuestionsAnswered + quiz.questions.length,
          totalCorrectAnswers: _userProgress!.totalCorrectAnswers + (completedQuiz.score ?? 0),
          lastStudyDate: DateTime.now(),
        );

        // Recalculate overall accuracy
        final newAccuracy = _userProgress!.totalCorrectAnswers / _userProgress!.totalQuestionsAnswered;
        _userProgress = _userProgress!.copyWith(overallAccuracy: newAccuracy);
      }

      // Analyze weaknesses
      await _analyzeWeaknesses();
      
      _setError(null);
    } catch (e) {
      _setError('Failed to complete quiz: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Analyze user weaknesses
  Future<void> _analyzeWeaknesses() async {
    if (_userProgress == null || _recentQuizzes.isEmpty) return;

    try {
      final analysis = await _aiService.analyzeUserWeaknesses(
        userProgress: _userProgress!,
        recentQuizzes: _recentQuizzes,
      );

      _userProgress = _userProgress!.copyWith(weaknessAnalysis: analysis);
    } catch (e) {
      // Weakness analysis failure shouldn't break the flow
      debugPrint('Weakness analysis failed: $e');
    }
  }

  // Get personalized recommendations
  Future<List<String>> getPersonalizedRecommendations() async {
    if (_userProgress?.weaknessAnalysis == null) {
      return [
        'Start with basic grammar topics to build your foundation',
        'Take practice quizzes regularly to identify your weak areas',
        'Focus on one topic at a time for better retention',
      ];
    }

    try {
      return await _aiService.generatePersonalizedRecommendations(
        weaknessAnalysis: _userProgress!.weaknessAnalysis,
        userProgress: _userProgress!,
      );
    } catch (e) {
      return [
        'Review your recent quiz results to identify patterns',
        'Focus on topics where your accuracy is below 70%',
        'Practice daily for consistent improvement',
      ];
    }
  }

  // Mark lesson as completed
  void completeLesson(String topicId, String lessonId) {
    final topicIndex = _topics.indexWhere((topic) => topic.id == topicId);
    if (topicIndex == -1) return;

    final topic = _topics[topicIndex];
    final lessonIndex = topic.lessons.indexWhere((lesson) => lesson.id == lessonId);
    if (lessonIndex == -1) return;

    final updatedLesson = topic.lessons[lessonIndex].copyWith(
      isCompleted: true,
      completedAt: DateTime.now(),
    );

    final updatedLessons = List<Lesson>.from(topic.lessons);
    updatedLessons[lessonIndex] = updatedLesson;

    final updatedTopic = topic.copyWith(lessons: updatedLessons);
    _topics[topicIndex] = updatedTopic;

    // Update user progress
    if (_userProgress != null) {
      _userProgress = _userProgress!.copyWith(
        lastStudyDate: DateTime.now(),
        totalStudyTimeMinutes: _userProgress!.totalStudyTimeMinutes + updatedLesson.estimatedTimeMinutes,
      );
    }

    notifyListeners();
  }

  // Get topics by difficulty
  List<GrammarTopic> getTopicsByDifficulty(DifficultyLevel difficulty) {
    return _topics.where((topic) => topic.difficulty == difficulty).toList();
  }

  // Get recommended topics based on weaknesses
  List<GrammarTopic> getRecommendedTopics() {
    if (_userProgress?.weaknessAnalysis.priorityTopics.isEmpty != false) {
      return _topics.take(3).toList();
    }

    final priorityTopicIds = _userProgress!.weaknessAnalysis.priorityTopics;
    final recommendedTopics = <GrammarTopic>[];

    for (final topicId in priorityTopicIds) {
      final topic = getTopicById(topicId);
      if (topic != null) {
        recommendedTopics.add(topic);
      }
    }

    return recommendedTopics;
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Get completion percentage for a topic
  double getTopicCompletionPercentage(String topicId) {
    final topic = getTopicById(topicId);
    if (topic == null || topic.lessons.isEmpty) return 0.0;

    final completedLessons = topic.lessons.where((lesson) => lesson.isCompleted).length;
    return (completedLessons / topic.lessons.length) * 100;
  }

  // Get user's overall progress percentage
  double get overallProgressPercentage {
    if (_topics.isEmpty) return 0.0;

    double totalProgress = 0.0;
    for (final topic in _topics) {
      totalProgress += getTopicCompletionPercentage(topic.id);
    }

    return totalProgress / _topics.length;
  }
}

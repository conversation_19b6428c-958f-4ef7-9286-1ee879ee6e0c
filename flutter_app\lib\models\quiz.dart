import 'quiz_question.dart';

enum QuizType { practice, assessment, weakness_focus }

enum QuizDifficulty { easy, medium, hard, mixed }

class Quiz {
  final String id;
  final String title;
  final String topicId;
  final QuizType type;
  final QuizDifficulty difficulty;
  final List<QuizQuestion> questions;
  final int timeLimit; // in minutes
  final DateTime createdAt;
  final bool isCompleted;
  final DateTime? completedAt;
  final int? score;
  final int? totalQuestions;
  final Map<String, int> categoryScores; // category -> score
  final List<String> weaknessesIdentified;

  const Quiz({
    required this.id,
    required this.title,
    required this.topicId,
    required this.type,
    required this.difficulty,
    required this.questions,
    required this.timeLimit,
    required this.createdAt,
    this.isCompleted = false,
    this.completedAt,
    this.score,
    this.totalQuestions,
    this.categoryScores = const {},
    this.weaknessesIdentified = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'topicId': topicId,
      'type': type.name,
      'difficulty': difficulty.name,
      'questions': questions.map((q) => q.toMap()).toList(),
      'timeLimit': timeLimit,
      'createdAt': createdAt.toIso8601String(),
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
      'score': score,
      'totalQuestions': totalQuestions,
      'categoryScores': categoryScores,
      'weaknessesIdentified': weaknessesIdentified,
    };
  }

  factory Quiz.fromMap(Map<String, dynamic> map) {
    return Quiz(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      topicId: map['topicId'] ?? '',
      type: QuizType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => QuizType.practice,
      ),
      difficulty: QuizDifficulty.values.firstWhere(
        (diff) => diff.name == map['difficulty'],
        orElse: () => QuizDifficulty.medium,
      ),
      questions: (map['questions'] as List<dynamic>?)
              ?.map((q) => QuizQuestion.fromMap(q))
              .toList() ??
          [],
      timeLimit: map['timeLimit'] ?? 30,
      createdAt: DateTime.parse(map['createdAt']),
      isCompleted: map['isCompleted'] ?? false,
      completedAt: map['completedAt'] != null
          ? DateTime.parse(map['completedAt'])
          : null,
      score: map['score'],
      totalQuestions: map['totalQuestions'],
      categoryScores: Map<String, int>.from(map['categoryScores'] ?? {}),
      weaknessesIdentified: List<String>.from(map['weaknessesIdentified'] ?? []),
    );
  }

  Quiz copyWith({
    String? id,
    String? title,
    String? topicId,
    QuizType? type,
    QuizDifficulty? difficulty,
    List<QuizQuestion>? questions,
    int? timeLimit,
    DateTime? createdAt,
    bool? isCompleted,
    DateTime? completedAt,
    int? score,
    int? totalQuestions,
    Map<String, int>? categoryScores,
    List<String>? weaknessesIdentified,
  }) {
    return Quiz(
      id: id ?? this.id,
      title: title ?? this.title,
      topicId: topicId ?? this.topicId,
      type: type ?? this.type,
      difficulty: difficulty ?? this.difficulty,
      questions: questions ?? this.questions,
      timeLimit: timeLimit ?? this.timeLimit,
      createdAt: createdAt ?? this.createdAt,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      score: score ?? this.score,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      categoryScores: categoryScores ?? this.categoryScores,
      weaknessesIdentified: weaknessesIdentified ?? this.weaknessesIdentified,
    );
  }

  double get scorePercentage {
    if (score == null || totalQuestions == null || totalQuestions == 0) {
      return 0.0;
    }
    return (score! / totalQuestions!) * 100;
  }

  String get typeText {
    switch (type) {
      case QuizType.practice:
        return 'Practice Quiz';
      case QuizType.assessment:
        return 'Assessment';
      case QuizType.weakness_focus:
        return 'Weakness Focus';
    }
  }

  String get difficultyText {
    switch (difficulty) {
      case QuizDifficulty.easy:
        return 'Easy';
      case QuizDifficulty.medium:
        return 'Medium';
      case QuizDifficulty.hard:
        return 'Hard';
      case QuizDifficulty.mixed:
        return 'Mixed';
    }
  }

  String get statusText {
    if (isCompleted) {
      return 'Completed - ${scorePercentage.toInt()}%';
    }
    return 'Not Started';
  }
}
